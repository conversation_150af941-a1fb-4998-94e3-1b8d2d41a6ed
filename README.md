<div align="center">
  <h1>🚀 XiaoBear Code Generator</h1>
  <p><strong>智能化企业级代码生成器</strong></p>

  [![License](https://img.shields.io/badge/License-AGPL--3.0-blue.svg)](LICENSE)
  [![Spring Boot](https://img.shields.io/badge/Spring%20Boot-2.7.18-brightgreen.svg)](https://spring.io/projects/spring-boot)
  [![Vue](https://img.shields.io/badge/Vue-3.5.12-4FC08D.svg)](https://vuejs.org/)
  [![Element Plus](https://img.shields.io/badge/Element%20Plus-2.8.7-409EFF.svg)](https://element-plus.org/)
  [![MyBatis](https://img.shields.io/badge/MyBatis-2.3.1-red.svg)](https://mybatis.org/)
</div>

## 📖 项目介绍

**XiaoBear Code Generator** 是一款基于若依RuoYi-Vue架构精心打造的**智能化企业级代码生成器**。它不仅仅是一个简单的代码生成工具，更是一个完整的开发效率提升解决方案。

### 🎯 核心价值

- **🚀 极速开发**：一键生成完整的前后端代码，减少90%的重复工作
- **🎨 灵活组合**：支持多种技术栈自由组合，满足不同项目需求
- **📊 智能分析**：基于数据库结构智能分析，生成符合企业级规范的高质量代码
- **🔧 开箱即用**：生成的代码包含完整的CRUD操作、数据校验、权限控制等功能
- **📚 文档齐全**：自动生成API文档、数据字典、项目文档等

### 🌟 适用场景

- **企业级管理系统**：快速构建CRM、ERP、OA等管理系统
- **微服务项目**：为微服务架构快速生成标准化的服务模块
- **原型开发**：快速搭建项目原型，验证业务逻辑
- **学习实践**：学习主流技术栈的最佳实践和代码规范

## 功能

- [x] 支持mybatis、mybaits-plus的后端代码生成（仅限于增删改查）
- [x] 支持element-ui与vue2、Vue3的代码生成
- [x] 支持ant-dv与vue的代码生成
- [x] 支持html的代码生成
- [x] 支持mysql脚本创建数据库表进行代码生成
- [x] 支持导出数据字典
- [x] 加入定时任务 清除导入的表结构
- [x] 数据源管理，多数据源的代码生成
- [x] 数据库结构文档多格式多数据源导出（word/html）

## 环境部署

```text
JDK >= 1.8 (推荐1.8版本)
Mysql >= 5.7.0
Maven >= 3.0
Node >= 10
```

1.  拉取代码，仓库地址：https://gitee.com/Xiao_bear/xiaobear-gen.git
2.  启动前端与后端

## 配置说明

1. 数据库连接配置`application.yml`

   ```yaml
   # 数据源配置
   spring:
       datasource:
           type: com.alibaba.druid.pool.DruidDataSource
           driverClassName: com.mysql.cj.jdbc.Driver
           druid:
               # 主库数据源
               master:
                   url: 数据库地址
                   username: 数据库账号
                   password: 数据库密码
   ```

   

2. 代码生成配置文件`generator.yml`

   ```yaml
   #代码生成
   gen: 
     # 作者
     author: xiaobear
     # 默认生成包路径 com.javaxiaobear 需改成自己的模块名称 如 system
     packageName: com.javaxiaobear
     # 自动去除表前缀，默认是false
     autoRemovePre: false
     # 表前缀（生成类名不会包含表前缀，多个用逗号分隔）
     tablePrefix:
     # 代码生成数据库
     dataBase: ly-generator
   ```

   - `dataBase`：若为空，则导入数据库脚本，默认选择当前连接的数据库

   也可连接若依环境的数据库，但需要增加如下两个字段：
   
   ```sql
   SET FOREIGN_KEY_CHECKS=0;
   
   ALTER TABLE `gen_table` ADD COLUMN `front_end` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '前端框架' AFTER `options`;
   
   ALTER TABLE `gen_table` ADD COLUMN `back_end` varchar(30) CHARACTER SET utf8 COLLATE utf8_general_ci NULL DEFAULT NULL COMMENT '后端框架' AFTER `front_end`;
   ```


## 使用说明

### 脚本代码生成

复制数据库脚本

![image-20211113175318992](images/image-20211113175318992.png)放入脚本

![image-20211113182100836](images/image-20211113182100836.png)



## 演示图
![img.png](images/img.png)
![img.png](images/img1.png)
![img_1.png](images/img_1.png)
![img_2.png](images/img_2.png)
![img_3.png](images/img_3.png)
![image-20220819130122486.png](images/image-20220819130122486.png)
## 联系

> 暂无提供QQ群，有问题可联系进行修复
<div align="center">
<img src="images/wechat.jpg" style="width: 60%;height: 100%;" referrerpolicy="no-referrer" alt="image-20210607222850467">
</div>

## 鸣谢

- 特别鸣谢：[RuoYi-Vue](https://gitee.com/y_project/RuoYi-Vue)

