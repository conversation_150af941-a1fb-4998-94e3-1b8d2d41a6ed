package com.javaxiaobear.controller;

import com.javaxiaobear.common.AjaxResult;
import com.javaxiaobear.common.BaseController;
import com.javaxiaobear.config.DatabaseSourceConfig;
import com.javaxiaobear.domain.DynamicDataSource;
import com.javaxiaobear.domain.GenTable;
import com.javaxiaobear.service.IDynamicDataSourceService;
import com.javaxiaobear.service.IGenTableService;
import com.javaxiaobear.util.DataSourceSwitcher;
import com.javaxiaobear.util.FileDownloadUtils;
import io.github.pomzwj.dbexport.core.DataBaseExportExecute;
import io.github.pomzwj.dbexport.core.domain.DbExportConfig;
import io.github.pomzwj.dbexport.core.type.ExportFileType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import javax.sql.DataSource;
import java.io.File;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;

/**
 * 数据库文档导出控制器
 *
 * <AUTHOR>
 * @version 1.0
 * @description: 数据库文档导出功能，支持Excel、Word、PDF等格式
 * @date 2025/1/8 16:16
 */
@RestController
@RequestMapping("/tool/gen")
public class DatabaseDictionaryExportController extends BaseController {

    @Autowired
    private IGenTableService genTableService;

    @Autowired
    private IDynamicDataSourceService dynamicDataSourceService;

    @Autowired
    private DatabaseSourceConfig databaseSourceConfig;

    @Autowired
    private DataSourceSwitcher dataSourceSwitcher;

    /**
     * 预览当前数据源的数据库表列表
     */
    @GetMapping("/preview/tables")
    public AjaxResult previewTables(@RequestParam(value = "dataSourceId", required = false) Long dataSourceId) {
        try {
            // 如果指定了数据源ID，临时切换到该数据源
            if (dataSourceId != null) {
                DatabaseSourceConfig.setCurrentDataSourceId(dataSourceId);
            }

            // 获取数据库表列表
            List<GenTable> tableList = dataSourceSwitcher.selectDbTableList(
                DatabaseSourceConfig.getCurrentDataSourceId(), null);

            return AjaxResult.success(tableList);
        } catch (Exception e) {
            logger.error("预览数据库表失败", e);
            return AjaxResult.error("预览数据库表失败：" + e.getMessage());
        } finally {
            // 清除数据源设置
            if (dataSourceId != null) {
                DatabaseSourceConfig.clearCurrentDataSourceId();
            }
        }
    }



    /**
     * 导出数据库文档
     */
    @PostMapping("/export")
    public AjaxResult exportDatabase(@RequestParam(value = "tableNames", required = false) String tableNames,
                                   @RequestParam(value = "exportType", defaultValue = "EXCEL") String exportType,
                                   @RequestParam(value = "dataSourceId", required = false) Long dataSourceId) {
        try {
            // 如果指定了数据源ID，临时切换到该数据源
            if (dataSourceId != null) {
                DatabaseSourceConfig.setCurrentDataSourceId(dataSourceId);
            }

            // 获取当前数据源
            DataSource dataSource = getCurrentDataSource();
            if (dataSource == null) {
                return AjaxResult.error("无法获取数据源");
            }

            // 配置导出参数
            DbExportConfig dbExportConfig = new DbExportConfig();

            // 设置导出类型
            ExportFileType fileType;
            String fileExtension;
            switch (exportType.toUpperCase()) {
                case "WORD":
                    fileType = ExportFileType.WORD;
                    fileExtension = "docx";
                    break;
                case "PDF":
                    fileType = ExportFileType.PDF;
                    fileExtension = "pdf";
                    break;
                case "HTML":
                    fileType = ExportFileType.HTML;
                    fileExtension = "html";
                    break;
                case "MARDOWN":
                    fileType = ExportFileType.MARKDOWN;
                    fileExtension = "md";
                    break;
                case "EXCEL":
                default:
                    // 暂时使用WORD格式代替EXCEL，因为ExportFileType.EXCEL不存在
                    fileType = ExportFileType.WORD;
                    fileExtension = "docx";
                    break;
            }

            dbExportConfig.setExportFileTypeEnum(fileType);

            // 设置导出的表名
            if (tableNames != null && !tableNames.trim().isEmpty()) {
                List<String> tableList = Arrays.asList(tableNames.split(","));
                dbExportConfig.setSelectTableList(tableList);
            }

            // 设置临时文件目录
            String tempDir = System.getProperty("java.io.tmpdir");
            dbExportConfig.setGenerationFileTempDir(tempDir);

            // 生成文件名
            String timestamp = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMdd_HHmmss"));
            String fileName = "数据库设计文档_" + timestamp + "." + fileExtension;

            // 执行导出
            String filePath = DataBaseExportExecute.executeFile(dataSource, dbExportConfig);

            // 返回文件路径，供下载使用
            Map<String, Object> result = new HashMap<>();
            result.put("filePath", filePath);
            result.put("fileName", fileName);
            result.put("fileSize", new File(filePath).length());

            return AjaxResult.success("文档导出成功", result);

        } catch (Exception e) {
            logger.error("导出数据库文档失败", e);
            return AjaxResult.error("导出数据库文档失败：" + e.getMessage());
        } finally {
            // 清除数据源设置
            if (dataSourceId != null) {
                DatabaseSourceConfig.clearCurrentDataSourceId();
            }
        }
    }

    /**
     * 下载导出的文件
     */
    @GetMapping("/download")
    public ResponseEntity<byte[]> downloadFile(@RequestParam("filePath") String filePath,
                                             @RequestParam("fileName") String fileName) {
        try {
            ResponseEntity<byte[]> response = FileDownloadUtils.downloadFileAsResponse(filePath, fileName);

            // 下载完成后删除临时文件
            new Thread(() -> {
                try {
                    Thread.sleep(5000); // 等待5秒确保下载完成
                    FileDownloadUtils.deleteTempFile(filePath);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }).start();

            return response;
        } catch (Exception e) {
            logger.error("文件下载失败", e);
            return ResponseEntity.status(500).build();
        }
    }

    /**
     * 下载导出的文件（直接响应流）
     */
    @GetMapping("/download/stream")
    public void downloadFileStream(@RequestParam("filePath") String filePath,
                                 @RequestParam("fileName") String fileName,
                                 HttpServletResponse response) {
        try {
            FileDownloadUtils.downloadFile(filePath, fileName, response);

            // 下载完成后删除临时文件
            new Thread(() -> {
                try {
                    Thread.sleep(5000); // 等待5秒确保下载完成
                    FileDownloadUtils.deleteTempFile(filePath);
                } catch (InterruptedException e) {
                    Thread.currentThread().interrupt();
                }
            }).start();

        } catch (Exception e) {
            logger.error("文件下载失败", e);
            try {
                response.sendError(HttpServletResponse.SC_INTERNAL_SERVER_ERROR, "文件下载失败");
            } catch (Exception ex) {
                logger.error("发送错误响应失败", ex);
            }
        }
    }

    /**
     * 获取支持的导出格式
     */
    @GetMapping("/formats")
    public AjaxResult getExportFormats() {
        List<Map<String, Object>> formats = new ArrayList<>();

        Map<String, Object> excel = new HashMap<>();
        excel.put("type", "EXCEL");
        excel.put("name", "Excel文档");
        excel.put("extension", "xlsx");
        formats.add(excel);

        Map<String, Object> word = new HashMap<>();
        word.put("type", "WORD");
        word.put("name", "Word文档");
        word.put("extension", "docx");
        formats.add(word);

        Map<String, Object> pdf = new HashMap<>();
        pdf.put("type", "PDF");
        pdf.put("name", "PDF文档");
        pdf.put("extension", "pdf");
        formats.add(pdf);

        Map<String, Object> html = new HashMap<>();
        html.put("type", "HTML");
        html.put("name", "HTML文档");
        html.put("extension", "html");
        formats.add(html);

        return AjaxResult.success(formats);
    }

    /**
     * 获取当前数据源
     */
    private DataSource getCurrentDataSource() {
        try {
            Long currentDataSourceId = DatabaseSourceConfig.getCurrentDataSourceId();
            if (currentDataSourceId != null) {
                DynamicDataSource dynamicDataSource = dynamicDataSourceService.selectDynamicDataSourceById(currentDataSourceId);
                if (dynamicDataSource != null) {
                    return databaseSourceConfig.getDataSourceByConfig(dynamicDataSource);
                }
            }

            // 使用默认数据源
            return databaseSourceConfig.createDefaultDataSource();
        } catch (Exception e) {
            logger.error("获取数据源失败", e);
            return null;
        }
    }

}
