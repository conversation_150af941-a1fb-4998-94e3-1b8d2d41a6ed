# 数据库配置
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ******************************************************************************************************************************************************************************************************
    username: root
    password: 123456
server:
  port: 8081
# 日志配置
logging:
  level:
    com.javaxiaobear: debug
    org.springframework: warn

# MyBatis配置
mybatis:
  # 搜索指定包别名
  typeAliasesPackage: com.javaxiaobear.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml

# SpringDoc配置
springdoc:
  api-docs:
    path: /v3/api-docs
  swagger-ui:
    path: /swagger-ui.html
    tags-sorter: alpha
    operations-sorter: alpha
  packages-to-scan: com.javaxiaobear.controller