# 生产环境配置
server:
  port: ${serverPort}
  servlet:
    context-path: /${artifactId}

spring:
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    driverClassName: ${driverClassName}
    druid:
      # 主库数据源
      master:
        url: ${datasourceUrl}
        username: ${datasourceUsername}
        password: ${datasourcePassword}
        # 初始连接数
        initialSize: 10
        # 最小连接池数量
        minIdle: 20
        # 最大连接池数量
        maxActive: 50
        # 配置获取连接等待超时的时间
        maxWait: 60000
        # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
        timeBetweenEvictionRunsMillis: 60000
        # 配置一个连接在池中最小生存的时间，单位是毫秒
        minEvictableIdleTimeMillis: 300000
        # 配置一个连接在池中最大生存的时间，单位是毫秒
        maxEvictableIdleTimeMillis: 900000
        # 配置检测连接是否有效
        validationQuery: SELECT 1 FROM DUAL
        testWhileIdle: true
        testOnBorrow: false
        testOnReturn: false
        webStatFilter:
          enabled: false
        statViewServlet:
          enabled: false
        filter:
          stat:
            enabled: true
            # 慢SQL记录
            log-slow-sql: true
            slow-sql-millis: 2000
            merge-sql: true
          wall:
            config:
              multi-statement-allow: true

  # Redis配置
  redis:
    # 地址
    host: localhost
    # 端口，默认为6379
    port: 6379
    # 数据库索引
    database: 0
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 10
        # 连接池中的最大空闲连接
        max-idle: 20
        # 连接池的最大数据库连接数
        max-active: 50
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms

# 日志配置
logging:
  level:
    ${packageName}: info
    org.springframework.security: warn
    org.springframework.web: warn
    org.mybatis: warn
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{20} - [%method,%line] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: ./logs/${artifactId}-prod.log
    max-size: 100MB
    max-history: 30

# MyBatis Plus配置
mybatis-plus:
  configuration:
    # 生产环境关闭SQL日志
    log-impl: org.apache.ibatis.logging.nologging.NoLoggingImpl
