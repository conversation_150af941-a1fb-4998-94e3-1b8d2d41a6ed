# ${projectComment}配置文件
server:
  port: ${serverPort}
  servlet:
    context-path: /${artifactId}

spring:
  application:
    name: ${artifactId}
  profiles:
    active: dev
  
  # 数据源配置
  datasource:
    type: com.alibaba.druid.pool.DruidDataSource
    druid:
      driver-class-name: ${driverClassName}
      url: ${datasourceUrl}
      username: ${datasourceUsername}
      password: ${datasourcePassword}
      
      # 初始连接数
      initial-size: 5
      # 最小连接池数量
      min-idle: 10
      # 最大连接池数量
      max-active: 20
      # 配置获取连接等待超时的时间
      max-wait: 60000
      # 配置间隔多久才进行一次检测，检测需要关闭的空闲连接，单位是毫秒
      time-between-eviction-runs-millis: 60000
      # 配置一个连接在池中最小生存的时间，单位是毫秒
      min-evictable-idle-time-millis: 300000
      # 配置一个连接在池中最大生存的时间，单位是毫秒
      max-evictable-idle-time-millis: 900000
      # 配置检测连接是否有效
      validation-query: SELECT 1 FROM DUAL
      test-while-idle: true
      test-on-borrow: false
      test-on-return: false
      
      # 配置监控统计拦截的filters，去掉后监控界面sql无法统计，'wall'用于防火墙
      filters: stat,wall,slf4j
      
      # 通过connectProperties属性来打开mergeSql功能；慢SQL记录
      connection-properties: druid.stat.mergeSql\=true;druid.stat.slowSqlMillis\=5000
      
      # 配置DruidStatFilter
      web-stat-filter:
        enabled: true
        url-pattern: "/*"
        exclusions: "*.js,*.gif,*.jpg,*.bmp,*.png,*.css,*.ico,/druid/*"
      
      # 配置DruidStatViewServlet
      stat-view-servlet:
        enabled: true
        url-pattern: "/druid/*"
        # IP白名单(没有配置或者为空，则允许所有访问)
        allow: 127.0.0.1,***********/16
        # IP黑名单 (存在共同时，deny优先于allow)
        deny: ************
        # 禁用HTML页面上的"Reset All"功能
        reset-enable: false
        # 登录名
        login-username: admin
        # 登录密码
        login-password: 123456

  # Jackson配置
  jackson:
    time-zone: GMT+8
    date-format: yyyy-MM-dd HH:mm:ss
    serialization:
      write-dates-as-timestamps: false
      fail-on-empty-beans: false
    deserialization:
      fail-on-unknown-properties: false

  # 文件上传配置
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 50MB

# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  type-aliases-package: ${packageName}.entity
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configuration:
    # 开启驼峰命名
    map-underscore-to-camel-case: true
    # 缓存数据到 PerpetualCache 的二级缓存，并且在每次查询时刷新缓存
    cache-enabled: false
    # 延迟加载的核心技术就是用代理模式，CGLIB/JAVASSIST两者选一
    lazy-loading-enabled: true
    # 每种数据库的执行器，SIMPLE 就是普通执行器；REUSE 执行器会重用预处理语句（prepared statements）； BATCH 执行器将重用语句并执行批量更新。
    default-executor-type: SIMPLE
    # 使用日志名字，不用类名
    use-actual-param-name: true
  global-config:
    db-config:
      # 主键策略
      id-type: ASSIGN_ID
      # 逻辑删除配置
      logic-delete-field: deleted
      logic-delete-value: 1
      logic-not-delete-value: 0

# JWT配置
jwt:
  # 密钥
  secret: ${jwtSecret}
  # token有效时长，7天，单位秒
  expire: 604800
  header: token

# 日志配置
logging:
  level:
    ${packageName}: debug
    org.springframework.security: debug
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{20} - [%method,%line] - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{50} - %msg%n"
  file:
    name: ./logs/${artifactId}.log

# Swagger配置
swagger:
  enabled: true
  title: ${projectComment}接口文档
  description: ${projectComment}接口文档
  version: ${version}
  base-package: ${packageName}.controller

# 项目自定义配置
project:
  name: ${projectName}
  version: ${version}
  copyrightYear: ${copyrightYear}
  # 文件路径 示例（ Windows配置D:/project/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: ./uploadPath
  # 获取ip地址开关
  addressEnabled: false
