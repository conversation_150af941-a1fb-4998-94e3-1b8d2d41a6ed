package ${packageName}.common;

import com.fasterxml.jackson.annotation.JsonInclude;

/**
 * 统一返回结果
 * 
 * <AUTHOR>
 * @date ${datetime}
 */
@JsonInclude(JsonInclude.Include.NON_NULL)
public class Result<T> {
    
    /** 成功状态码 */
    public static final int SUCCESS = 200;
    
    /** 失败状态码 */
    public static final int ERROR = 500;
    
    /** 状态码 */
    private int code;
    
    /** 返回消息 */
    private String message;
    
    /** 返回数据 */
    private T data;
    
    public Result() {}
    
    public Result(int code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public Result(int code, String message, T data) {
        this.code = code;
        this.message = message;
        this.data = data;
    }
    
    /**
     * 成功返回结果
     */
    public static <T> Result<T> success() {
        return new Result<>(SUCCESS, "操作成功");
    }
    
    /**
     * 成功返回结果
     */
    public static <T> Result<T> success(T data) {
        return new Result<>(SUCCESS, "操作成功", data);
    }
    
    /**
     * 成功返回结果
     */
    public static <T> Result<T> success(String message, T data) {
        return new Result<>(SUCCESS, message, data);
    }
    
    /**
     * 失败返回结果
     */
    public static <T> Result<T> error() {
        return new Result<>(ERROR, "操作失败");
    }
    
    /**
     * 失败返回结果
     */
    public static <T> Result<T> error(String message) {
        return new Result<>(ERROR, message);
    }
    
    /**
     * 失败返回结果
     */
    public static <T> Result<T> error(int code, String message) {
        return new Result<>(code, message);
    }
    
    // Getters and Setters
    public int getCode() {
        return code;
    }
    
    public void setCode(int code) {
        this.code = code;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public T getData() {
        return data;
    }
    
    public void setData(T data) {
        this.data = data;
    }
}
