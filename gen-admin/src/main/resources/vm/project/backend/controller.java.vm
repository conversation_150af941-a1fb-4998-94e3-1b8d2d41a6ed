package ${packageName}.controller;

import java.util.List;
import javax.annotation.Resource;
import org.springframework.web.bind.annotation.*;
#if($isRuoYi)
import org.springframework.security.access.prepost.PreAuthorize;
import ${basePackageName}.common.annotation.Log;
import ${basePackageName}.common.core.controller.BaseController;
import ${basePackageName}.common.core.domain.AjaxResult;
import ${basePackageName}.common.enums.BusinessType;
import ${basePackageName}.common.core.page.TableDataInfo;
#end
import ${packageName}.domain.${ClassName};
import ${packageName}.service.I${ClassName}Service;
import ${basePackageName}.common.utils.poi.ExcelUtil;
#if(!$isRuoYi)
import org.springframework.http.ResponseEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
#end

/**
 * ${functionName}Controller
 *
 * <AUTHOR>
 * @date ${datetime}
 */
@RestController
@RequestMapping("/${moduleName}/${businessName}")
#if($isRuoYi)
public class ${ClassName}Controller extends BaseController
#else
public class ${ClassName}Controller
#end
{
    @Resource
    private I${ClassName}Service ${className}Service;

    /**
     * 查询${functionName}列表
     */
    #if($isRuoYi)
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:list')")
    @GetMapping("/list")
    public TableDataInfo list(${ClassName} ${className}) {
        startPage();
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        return getDataTable(list);
    }
#else
    @GetMapping("/list")
    public List<${ClassName}> list(${ClassName} ${className}) {
        return ${className}Service.select${ClassName}List(${className});
    }
#end

    /**
     * 导出${functionName}列表
     */
    #if($isRuoYi)
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:export')")
    @Log(title = "${functionName}", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(${ClassName} ${className}) {
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        ExcelUtil<${ClassName}> util = new ExcelUtil<>(${ClassName}.class);
        return util.exportExcel(list, "${functionName}数据");
    }
#else
    @GetMapping("/export")
    public ResponseEntity<byte[]> export(${ClassName} ${className}) {
        List<${ClassName}> list = ${className}Service.select${ClassName}List(${className});
        ExcelUtil<${ClassName}> util = new ExcelUtil<>(${ClassName}.class);
        byte[] data = util.exportExcelToBytes(list, "${functionName}数据");
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_OCTET_STREAM);
        headers.setContentDispositionFormData("attachment", "${functionName}数据.xlsx");
        return ResponseEntity.ok().headers(headers).body(data);
    }
#end

    /**
     * 获取${functionName}详细信息
     */
#if($isRuoYi)
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:query')")
    @GetMapping(value = "/{${pkColumn.javaField}}")
    public AjaxResult getInfo(@PathVariable("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField}) {
        return AjaxResult.success(${className}Service.select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField}));
    }
#else
    @GetMapping(value = "/{${pkColumn.javaField}}")
    public ${ClassName} getInfo(@PathVariable("${pkColumn.javaField}") ${pkColumn.javaType} ${pkColumn.javaField}) {
        return ${className}Service.select${ClassName}By${pkColumn.capJavaField}(${pkColumn.javaField});
    }
#end

    /**
     * 新增${functionName}
     */
#if($isRuoYi)
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:add')")
    @Log(title = "${functionName}", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ${ClassName} ${className}) {
        return toAjax(${className}Service.insert${ClassName}(${className}));
    }
#else
    @PostMapping
    public int add(@RequestBody ${ClassName} ${className}) {
        return ${className}Service.insert${ClassName}(${className});
    }
#end

    /**
     * 修改${functionName}
     */
#if($isRuoYi)
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:edit')")
    @Log(title = "${functionName}", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ${ClassName} ${className}) {
        return toAjax(${className}Service.update${ClassName}(${className}));
    }
 #else
    @PutMapping
    public int edit(@RequestBody ${ClassName} ${className}) {
        return ${className}Service.update${ClassName}(${className});
    }
#end

    /**
     * 删除${functionName}
     */
#if($isRuoYi)
    @PreAuthorize("@ss.hasPermi('${permissionPrefix}:remove')")
    @Log(title = "${functionName}", businessType = BusinessType.DELETE)
    @DeleteMapping("/{${pkColumn.javaField}s}")
    public AjaxResult remove(@PathVariable ${pkColumn.javaType}[] ${pkColumn.javaField}s) {
        return toAjax(${className}Service.delete${ClassName}By${pkColumn.capJavaField}s(${pkColumn.javaField}s));
    }
#else
    @DeleteMapping("/{${pkColumn.javaField}s}")
    public int remove(@PathVariable ${pkColumn.javaType}[] ${pkColumn.javaField}s) {
        return ${className}Service.delete${ClassName}By${pkColumn.capJavaField}s(${pkColumn.javaField}s);
    }
#end
}
