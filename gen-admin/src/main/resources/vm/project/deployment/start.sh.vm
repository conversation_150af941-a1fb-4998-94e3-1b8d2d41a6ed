#!/bin/bash

# ${projectComment} 项目启动脚本
# 作者: ${author}
# 版本: ${version}

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_NAME="${projectName}"
BACKEND_DIR="${projectName}-backend"
FRONTEND_DIR="${projectName}-frontend"
BACKEND_PORT=${serverPort}
FRONTEND_PORT=8080

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}    ${projectComment} 项目启动脚本${NC}"
echo -e "${BLUE}========================================${NC}"

# 检查依赖
check_dependencies() {
    echo -e "${YELLOW}检查系统依赖...${NC}"
    
    # 检查Java
    if ! command -v java &> /dev/null; then
        echo -e "${RED}错误: 未找到Java，请安装Java 8或更高版本${NC}"
        exit 1
    fi
    
    # 检查Maven
    if ! command -v mvn &> /dev/null; then
        echo -e "${RED}错误: 未找到Maven，请安装Maven 3.0或更高版本${NC}"
        exit 1
    fi
    
    # 检查Node.js
    if ! command -v node &> /dev/null; then
        echo -e "${RED}错误: 未找到Node.js，请安装Node.js 16或更高版本${NC}"
        exit 1
    fi
    
    # 检查npm
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}错误: 未找到npm${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✓ 系统依赖检查通过${NC}"
}

# 启动后端
start_backend() {
    echo -e "${YELLOW}启动后端服务...${NC}"
    
    if [ ! -d "$BACKEND_DIR" ]; then
        echo -e "${RED}错误: 后端目录 $BACKEND_DIR 不存在${NC}"
        exit 1
    fi
    
    cd "$BACKEND_DIR"
    
    # 检查是否已编译
    if [ ! -f "target/${artifactId}-${version}.jar" ]; then
        echo -e "${YELLOW}编译后端项目...${NC}"
        mvn clean package -DskipTests
    fi
    
    # 启动后端服务
    echo -e "${YELLOW}启动后端服务，端口: $BACKEND_PORT${NC}"
    nohup mvn spring-boot:run > backend.log 2>&1 &
    BACKEND_PID=$!
    echo $BACKEND_PID > backend.pid
    
    # 等待后端启动
    echo -e "${YELLOW}等待后端服务启动...${NC}"
    for i in {1..30}; do
        if curl -s http://localhost:$BACKEND_PORT/actuator/health > /dev/null 2>&1; then
            echo -e "${GREEN}✓ 后端服务启动成功 (PID: $BACKEND_PID)${NC}"
            break
        fi
        sleep 2
        if [ $i -eq 30 ]; then
            echo -e "${RED}错误: 后端服务启动超时${NC}"
            exit 1
        fi
    done
    
    cd ..
}

# 启动前端
start_frontend() {
    echo -e "${YELLOW}启动前端服务...${NC}"
    
    if [ ! -d "$FRONTEND_DIR" ]; then
        echo -e "${RED}错误: 前端目录 $FRONTEND_DIR 不存在${NC}"
        exit 1
    fi
    
    cd "$FRONTEND_DIR"
    
    # 安装依赖
    if [ ! -d "node_modules" ]; then
        echo -e "${YELLOW}安装前端依赖...${NC}"
        npm install
    fi
    
    # 启动前端服务
    echo -e "${YELLOW}启动前端服务，端口: $FRONTEND_PORT${NC}"
    nohup npm run dev > frontend.log 2>&1 &
    FRONTEND_PID=$!
    echo $FRONTEND_PID > frontend.pid
    
    # 等待前端启动
    echo -e "${YELLOW}等待前端服务启动...${NC}"
    for i in {1..20}; do
        if curl -s http://localhost:$FRONTEND_PORT > /dev/null 2>&1; then
            echo -e "${GREEN}✓ 前端服务启动成功 (PID: $FRONTEND_PID)${NC}"
            break
        fi
        sleep 3
        if [ $i -eq 20 ]; then
            echo -e "${RED}错误: 前端服务启动超时${NC}"
            exit 1
        fi
    done
    
    cd ..
}

# 显示启动信息
show_info() {
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}    ${projectComment} 启动完成！${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}后端服务: http://localhost:$BACKEND_PORT${NC}"
    echo -e "${GREEN}前端应用: http://localhost:$FRONTEND_PORT${NC}"
    echo -e "${GREEN}API文档: http://localhost:$BACKEND_PORT/swagger-ui.html${NC}"
    echo -e "${GREEN}========================================${NC}"
    echo -e "${YELLOW}使用 ./stop.sh 停止服务${NC}"
}

# 主函数
main() {
    check_dependencies
    start_backend
    start_frontend
    show_info
}

# 执行主函数
main
