#!/bin/bash

# ${projectComment} 项目停止脚本
# 作者: ${author}
# 版本: ${version}

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目配置
PROJECT_NAME="${projectName}"
BACKEND_DIR="${projectName}-backend"
FRONTEND_DIR="${projectName}-frontend"

echo -e "${BLUE}========================================${NC}"
echo -e "${BLUE}    ${projectComment} 项目停止脚本${NC}"
echo -e "${BLUE}========================================${NC}"

# 停止后端服务
stop_backend() {
    echo -e "${YELLOW}停止后端服务...${NC}"
    
    if [ -f "$BACKEND_DIR/backend.pid" ]; then
        BACKEND_PID=$(cat "$BACKEND_DIR/backend.pid")
        if ps -p $BACKEND_PID > /dev/null 2>&1; then
            kill $BACKEND_PID
            echo -e "${GREEN}✓ 后端服务已停止 (PID: $BACKEND_PID)${NC}"
        else
            echo -e "${YELLOW}后端服务进程不存在${NC}"
        fi
        rm -f "$BACKEND_DIR/backend.pid"
    else
        echo -e "${YELLOW}未找到后端服务PID文件${NC}"
    fi
}

# 停止前端服务
stop_frontend() {
    echo -e "${YELLOW}停止前端服务...${NC}"
    
    if [ -f "$FRONTEND_DIR/frontend.pid" ]; then
        FRONTEND_PID=$(cat "$FRONTEND_DIR/frontend.pid")
        if ps -p $FRONTEND_PID > /dev/null 2>&1; then
            kill $FRONTEND_PID
            echo -e "${GREEN}✓ 前端服务已停止 (PID: $FRONTEND_PID)${NC}"
        else
            echo -e "${YELLOW}前端服务进程不存在${NC}"
        fi
        rm -f "$FRONTEND_DIR/frontend.pid"
    else
        echo -e "${YELLOW}未找到前端服务PID文件${NC}"
    fi
}

# 清理相关进程
cleanup_processes() {
    echo -e "${YELLOW}清理相关进程...${NC}"
    
    # 查找并停止可能的相关进程
    pkill -f "${artifactId}" || true
    pkill -f "vue-cli-service" || true
    
    echo -e "${GREEN}✓ 进程清理完成${NC}"
}

# 主函数
main() {
    stop_backend
    stop_frontend
    cleanup_processes
    
    echo -e "${GREEN}========================================${NC}"
    echo -e "${GREEN}    ${projectComment} 已完全停止${NC}"
    echo -e "${GREEN}========================================${NC}"
}

# 执行主函数
main
