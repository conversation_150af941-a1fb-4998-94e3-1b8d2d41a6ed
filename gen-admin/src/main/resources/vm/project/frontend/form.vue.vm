<template>
  <div class="form-container">
    <el-card class="form-card">
      <template #header>
        <div class="card-header">
          <span>{{ isEdit ? '编辑' : '新增' }}${functionName}</span>
          <el-button type="text" @click="handleClose">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </template>
      
      <el-form
        ref="formRef"
        :model="form"
        :rules="rules"
        label-width="120px"
        class="form-content"
      >
#foreach($column in $columns)
#if($column.insert && !$column.pk)
#if($column.htmlType == "input")
        <el-form-item label="${column.columnComment}" prop="${column.javaField}">
          <el-input
            v-model="form.${column.javaField}"
            placeholder="请输入${column.columnComment}"
#if($column.javaType == 'Integer' || $column.javaType == 'Long')
            type="number"
#end
          />
        </el-form-item>
#elseif($column.htmlType == "textarea")
        <el-form-item label="${column.columnComment}" prop="${column.javaField}">
          <el-input
            v-model="form.${column.javaField}"
            type="textarea"
            placeholder="请输入${column.columnComment}"
            :rows="4"
          />
        </el-form-item>
#elseif($column.htmlType == "select")
        <el-form-item label="${column.columnComment}" prop="${column.javaField}">
          <el-select
            v-model="form.${column.javaField}"
            placeholder="请选择${column.columnComment}"
            style="width: 100%"
          >
            <el-option
              v-for="dict in ${column.javaField}Options"
              :key="dict.value"
              :label="dict.label"
              :value="dict.value"
            />
          </el-select>
        </el-form-item>
#elseif($column.htmlType == "radio")
        <el-form-item label="${column.columnComment}" prop="${column.javaField}">
          <el-radio-group v-model="form.${column.javaField}">
            <el-radio
              v-for="dict in ${column.javaField}Options"
              :key="dict.value"
              :label="dict.value"
            >
              {{ dict.label }}
            </el-radio>
          </el-radio-group>
        </el-form-item>
#elseif($column.htmlType == "checkbox")
        <el-form-item label="${column.columnComment}" prop="${column.javaField}">
          <el-checkbox-group v-model="form.${column.javaField}">
            <el-checkbox
              v-for="dict in ${column.javaField}Options"
              :key="dict.value"
              :label="dict.value"
            >
              {{ dict.label }}
            </el-checkbox>
          </el-checkbox-group>
        </el-form-item>
#elseif($column.htmlType == "datetime")
        <el-form-item label="${column.columnComment}" prop="${column.javaField}">
          <el-date-picker
            v-model="form.${column.javaField}"
            type="datetime"
            placeholder="选择${column.columnComment}"
            style="width: 100%"
          />
        </el-form-item>
#else
        <el-form-item label="${column.columnComment}" prop="${column.javaField}">
          <el-input
            v-model="form.${column.javaField}"
            placeholder="请输入${column.columnComment}"
          />
        </el-form-item>
#end
#end
#end
      </el-form>
      
      <div class="form-footer">
        <el-button @click="handleClose">取消</el-button>
        <el-button type="primary" @click="handleSubmit" :loading="loading">
          {{ isEdit ? '更新' : '创建' }}
        </el-button>
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, reactive, computed, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { Close } from '@element-plus/icons-vue'
import { add${ClassName}, update${ClassName}, get${ClassName} } from '@/api/${businessName}'

const props = defineProps({
  id: {
    type: [String, Number],
    default: null
  }
})

const emit = defineEmits(['close', 'success'])

const formRef = ref()
const loading = ref(false)

// 判断是否为编辑模式
const isEdit = computed(() => !!props.id)

// 表单数据
const form = reactive({
#foreach($column in $columns)
#if($column.insert && !$column.pk)
  ${column.javaField}: #if($column.javaType == 'Integer' || $column.javaType == 'Long')0#elseif($column.javaType == 'Date')null#else''#end,
#end
#end
})

// 表单验证规则
const rules = reactive({
#foreach($column in $columns)
#if($column.insert && !$column.pk && $column.required)
  ${column.javaField}: [
    { required: true, message: '请输入${column.columnComment}', trigger: 'blur' }
  ],
#end
#end
})

// 字典选项数据
#foreach($column in $columns)
#if($column.htmlType == "select" || $column.htmlType == "radio" || $column.htmlType == "checkbox")
const ${column.javaField}Options = ref([])
#end
#end

// 初始化
onMounted(() => {
  if (isEdit.value) {
    loadData()
  }
  // 加载字典数据
  loadDictData()
})

// 加载数据
const loadData = async () => {
  try {
    const response = await get${ClassName}(props.id)
    Object.assign(form, response.data)
  } catch (error) {
    ElMessage.error('加载数据失败')
  }
}

// 加载字典数据
const loadDictData = () => {
  // 这里可以加载字典数据
#foreach($column in $columns)
#if($column.htmlType == "select" || $column.htmlType == "radio" || $column.htmlType == "checkbox")
  // ${column.javaField}Options.value = []
#end
#end
}

// 提交表单
const handleSubmit = async () => {
  try {
    await formRef.value.validate()
    loading.value = true
    
    if (isEdit.value) {
      await update${ClassName}(form)
      ElMessage.success('更新成功')
    } else {
      await add${ClassName}(form)
      ElMessage.success('创建成功')
    }
    
    emit('success')
    handleClose()
  } catch (error) {
    if (error !== false) {
      ElMessage.error(isEdit.value ? '更新失败' : '创建失败')
    }
  } finally {
    loading.value = false
  }
}

// 关闭表单
const handleClose = () => {
  emit('close')
}
</script>

<style scoped>
.form-container {
  padding: 20px;
}

.form-card {
  max-width: 800px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.form-content {
  padding: 20px 0;
}

.form-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}
</style>
