{"name": "${artifactId}-frontend", "version": "${version}", "description": "${projectComment}前端项目", "author": "${author}", "private": true, "scripts": {"dev": "vue-cli-service serve --mode development", "build": "vue-cli-service build --mode production", "build:dev": "vue-cli-service build --mode development", "build:test": "vue-cli-service build --mode test", "preview": "node build/index.js", "lint": "eslint --ext .js,.vue src"}, "dependencies": {"vue": "^3.3.4", "vue-router": "^4.2.4", "vuex": "^4.0.2", "element-plus": "^2.3.8", "@element-plus/icons-vue": "^2.1.0", "axios": "^1.4.0", "js-cookie": "^3.0.5", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "path-to-regexp": "^6.2.1", "screenfull": "^6.0.2", "sortablejs": "^1.15.0"}, "devDependencies": {"@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-plugin-vuex": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/eslint-config-standard": "^8.0.1", "eslint": "^8.45.0", "eslint-plugin-import": "^2.27.5", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^6.1.1", "eslint-plugin-standard": "^5.0.0", "eslint-plugin-vue": "^9.15.1", "sass": "^1.64.1", "sass-loader": "^13.3.2", "svg-sprite-loader": "^6.0.11"}, "engines": {"node": ">=16.0.0", "npm": ">= 6.0.0"}, "browserslist": ["> 1%", "last 2 versions"]}