{"name": "vue-front-template", "version": "1.1.0", "author": "java<PERSON><PERSON>ar", "description": "小熊代码生成", "license": "AGPL-3.0", "private": true, "engines": {"node": ">=20.0.0", "npm": ">=9.0.0"}, "scripts": {"dev": "vue-cli-service serve --mode dev", "serve": "npm run dev", "build": "vue-cli-service build --mode prod", "build:dev": "vue-cli-service build --mode dev", "build:test": "vue-cli-service build --mode test", "build:pre": "vue-cli-service build --mode pre", "build:prod": "vue-cli-service build --mode prod", "build:analyze": "vue-cli-service build --mode prod --report", "preview": "npm run build && npx http-server dist -p 8080", "lint": "vue-cli-service lint", "lint:fix": "vue-cli-service lint --fix", "audit": "npm audit", "audit:fix": "npm audit fix", "deps:check": "npm-check-updates", "deps:update": "npm-check-updates -u", "clean": "rimraf dist node_modules/.cache", "reinstall": "npm run clean && npm install", "test": "echo \"No tests specified\" && exit 0", "format": "prettier --write \"src/**/*.{js,vue,scss,css}\"", "format:check": "prettier --check \"src/**/*.{js,vue,scss,css}\""}, "husky": {"hooks": {"pre-commit": "lint-staged"}}, "lint-staged": {"src/**/*.{js,vue}": ["eslint --fix", "git add"]}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "axios": "^1.7.2", "clipboard": "^2.0.11", "core-js": "^3.43.0", "element-plus": "^2.8.7", "file-saver": "^2.0.5", "highlight.js": "^11.10.0", "js-cookie": "^3.0.5", "normalize.css": "^8.0.1", "nprogress": "^0.2.0", "sortablejs": "^1.15.2", "vue": "^3.5.12", "vue-router": "^4.4.5", "vuex": "^4.1.0"}, "devDependencies": {"@babel/eslint-parser": "^7.24.7", "@vue/cli-plugin-babel": "^5.0.8", "@vue/cli-plugin-eslint": "^5.0.8", "@vue/cli-plugin-router": "^5.0.8", "@vue/cli-plugin-vuex": "^5.0.8", "@vue/cli-service": "^5.0.8", "@vue/eslint-config-standard": "^8.0.1", "cache-loader": "^4.1.0", "compression-webpack-plugin": "^11.1.0", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.27.0", "npm-check-updates": "^17.1.1", "sass": "^1.77.6", "sass-loader": "^14.2.1", "svg-sprite-loader": "^6.0.11", "webpack-bundle-analyzer": "^4.10.2"}}