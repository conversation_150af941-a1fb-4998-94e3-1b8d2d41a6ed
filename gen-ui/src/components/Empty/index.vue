<template>
  <div class="empty-container">
    <div class="empty-content">
      <div class="empty-icon">
        <i :class="icon"></i>
      </div>
      <div class="empty-title">{{ title }}</div>
      <div class="empty-description" v-if="description">{{ description }}</div>
      <div class="empty-actions" v-if="$slots.default">
        <slot></slot>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Empty',
  props: {
    icon: {
      type: String,
      default: 'el-icon-box'
    },
    title: {
      type: String,
      default: '暂无数据'
    },
    description: {
      type: String,
      default: ''
    }
  }
}
</script>

<style lang="scss" scoped>
.empty-container {
  padding: var(--spacing-xl) var(--spacing-lg);
  text-align: center;
}

.empty-content {
  max-width: 400px;
  margin: 0 auto;
}

.empty-icon {
  margin-bottom: var(--spacing-lg);
  
  i {
    font-size: 4rem;
    color: var(--text-placeholder);
    opacity: 0.6;
  }
}

.empty-title {
  font-size: 1.2rem;
  font-weight: 500;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
}

.empty-description {
  color: var(--text-placeholder);
  line-height: 1.6;
  margin-bottom: var(--spacing-lg);
}

.empty-actions {
  margin-top: var(--spacing-lg);
}
</style>
