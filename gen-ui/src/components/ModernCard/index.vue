<template>
  <div class="modern-card" :class="[
    `card-${type}`,
    { 'card-hover': hover, 'card-interactive': interactive, 'card-elevated': elevated }
  ]">
    <div class="card-header" v-if="title || $slots.header">
      <slot name="header">
        <div class="card-title-wrapper">
          <div class="card-icon" v-if="icon">
            <el-icon><component :is="icon" /></el-icon>
          </div>
          <h3 class="card-title">{{ title }}</h3>
        </div>
        <div class="card-actions" v-if="$slots.actions">
          <slot name="actions"></slot>
        </div>
      </slot>
    </div>
    
    <div class="card-body" :class="{ 'no-padding': noPadding }">
      <slot></slot>
    </div>
    
    <div class="card-footer" v-if="$slots.footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script>
export default {
  name: 'ModernCard',
  props: {
    title: {
      type: String,
      default: ''
    },
    icon: {
      type: String,
      default: ''
    },
    type: {
      type: String,
      default: 'default',
      validator: value => ['default', 'primary', 'success', 'warning', 'danger', 'info'].includes(value)
    },
    hover: {
      type: Boolean,
      default: true
    },
    interactive: {
      type: Boolean,
      default: false
    },
    elevated: {
      type: Boolean,
      default: false
    },
    noPadding: {
      type: Boolean,
      default: false
    }
  }
}
</script>

<style lang="scss" scoped>
.modern-card {
  background: var(--bg-primary);
  border-radius: var(--border-radius-large);
  border: 1px solid var(--border-light);
  transition: var(--transition-base);
  overflow: hidden;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: var(--border-light);
    transition: var(--transition-base);
  }

  &.card-hover:hover {
    transform: translateY(-4px);
    box-shadow: var(--shadow-dark);
    border-color: var(--primary-color);

    &::before {
      background: var(--primary-gradient);
    }
  }

  &.card-interactive {
    cursor: pointer;

    &:active {
      transform: translateY(-2px);
    }
  }

  &.card-elevated {
    box-shadow: var(--shadow-base);
  }

  // 卡片类型样式
  &.card-primary::before {
    background: var(--primary-gradient);
  }

  &.card-success::before {
    background: linear-gradient(135deg, #67c23a, #85ce61);
  }

  &.card-warning::before {
    background: linear-gradient(135deg, #e6a23c, #ebb563);
  }

  &.card-danger::before {
    background: linear-gradient(135deg, #f56c6c, #f78989);
  }

  &.card-info::before {
    background: linear-gradient(135deg, #909399, #a6a9ad);
  }

  .card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--border-light);
    background: var(--bg-secondary);
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card-title-wrapper {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);

      .card-icon {
        width: 32px;
        height: 32px;
        display: flex;
        align-items: center;
        justify-content: center;
        background: var(--primary-gradient);
        border-radius: var(--border-radius-base);
        color: white;
        font-size: 16px;
      }

      .card-title {
        margin: 0;
        font-size: 18px;
        font-weight: 600;
        color: var(--text-primary);
      }
    }

    .card-actions {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
    }
  }

  .card-body {
    padding: var(--spacing-lg);

    &.no-padding {
      padding: 0;
    }
  }

  .card-footer {
    padding: var(--spacing-md) var(--spacing-lg);
    border-top: 1px solid var(--border-light);
    background: var(--bg-tertiary);
    display: flex;
    justify-content: flex-end;
    gap: var(--spacing-sm);
  }
}

// 深色主题适配
[data-theme="dark"] {
  .modern-card {
    background: var(--bg-secondary);
    border-color: var(--border-dark);

    .card-header {
      background: var(--bg-tertiary);
      border-bottom-color: var(--border-dark);
    }

    .card-footer {
      background: var(--bg-tertiary);
      border-top-color: var(--border-dark);
    }
  }
}
</style>