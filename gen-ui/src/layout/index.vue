<template>
  <el-container class="layout-container">
    <el-header class="gen-header" :class="{ scrolled: isScrolled }">
      <div class="header-content">
        <div class="logo-container">
          <router-link to="/" class="logo-link" @click="handleLogoClick">
            <img src="@/assets/logo.png" class="logo" alt="小熊代码生成器"/>
            <span class="logo-text">小熊代码生成器</span>
            <div class="logo-ripple" :class="{ active: logoClicked }"></div>
          </router-link>
        </div>
        <nav class="nav-menu">
          <router-link
            v-for="item in menuItems"
            :key="item.path"
            :to="item.path"
            class="nav-item"
            :class="{ active: isActive(item.path) }"
            @click="handleNavClick(item)"
          >
            <el-icon><component :is="getIconComponent(item.icon)" /></el-icon>
            <span>{{ item.title }}</span>
            <div class="nav-indicator"></div>
          </router-link>
        </nav>
        <div class="header-actions">
          <el-tooltip content="访问GitHub仓库" placement="bottom" :open-delay="500">
            <a
              href="https://github.com/javaxiaobear/xiaobear-gen"
              target="_blank"
              class="action-btn"
              @click="handleGithubClick"
            >
              <el-icon><Link /></el-icon>
              <span class="action-text">GitHub</span>
            </a>
          </el-tooltip>
          <el-tooltip content="切换主题" placement="bottom" :open-delay="500">
            <button class="action-btn theme-toggle" @click="toggleTheme">
              <el-icon><component :is="isDark ? 'Sunny' : 'Moon'" /></el-icon>
            </button>
          </el-tooltip>
        </div>
      </div>
    </el-header>
    <el-main class="gen-main">
      <div class="main-content">
        <router-view/>
      </div>
    </el-main>
    <el-footer class="gen-footer">
      <div class="footer-content">
        <div class="footer-info">
          <span>MIT Licensed | Copyright © 2021-present</span>
          <a href="https://www.javaxiaobear.cn" target="_blank" class="footer-link">小熊学Java</a>
        </div>
        <div class="footer-links">
          <a href="https://github.com/javaxiaobear/xiaobear-gen" target="_blank">GitHub</a>
          <a href="https://www.javaxiaobear.cn" target="_blank">官网</a>
        </div>
      </div>
    </el-footer>
  </el-container>
</template>

<script>
import { House, Document, Connection, Folder, Link, Sunny, Moon, Star } from '@element-plus/icons-vue'
import { getNewIconName, isOldElementIcon } from '@/utils/icon-mapping'

export default {
  name: 'Layout',
  components: {
    House,
    Document,
    Connection,
    Folder,
    Link,
    Sunny,
    Moon,
    Star
  },
  data() {
    return {
      isScrolled: false,
      logoClicked: false,
      isDark: false,
      menuItems: [
        {
          path: '/index',
          title: '首页',
          icon: 'House',
          description: '项目介绍和快速开始'
        },
        {
          path: '/generate/index',
          title: '代码生成',
          icon: 'Document',
          description: '智能生成高质量代码'
        },
        // {
        //   path: '/smart-factory/index',
        //   title: '智能代码工厂',
        //   icon: 'MagicStick',
        //   description: '一键生成完整全栈项目'
        // },
        {
          path: '/dataSource/index',
          title: '数据源管理',
          icon: 'Connection',
          description: '管理数据库连接配置'
        },
        {
          path: '/export/index',
          title: '数据库文档导出',
          icon: 'Folder',
          description: '导出数据库设计文档'
        },
        {
          path: '/changelog/index',
          title: '更新日志',
          icon: 'Star',
          description: '更新日志'
        }
      ]
    }
  },
  mounted() {
    window.addEventListener('scroll', this.handleScroll)
  },
  beforeUnmount() {
    window.removeEventListener('scroll', this.handleScroll)
  },
  methods: {
    handleScroll() {
      this.isScrolled = window.scrollY > 10
    },
    isActive(path) {
      if (path === '/index') {
        return this.$route.path === '/' || this.$route.path === '/index'
      }
      return this.$route.path.startsWith(path.split('/')[1] ? `/${path.split('/')[1]}` : path)
    },
    handleLogoClick() {
      this.logoClicked = true
      setTimeout(() => {
        this.logoClicked = false
      }, 600)

      // 添加页面切换动画
      this.$nextTick(() => {
        document.body.classList.add('page-transition')
        setTimeout(() => {
          document.body.classList.remove('page-transition')
        }, 300)
      })
    },
    handleNavClick(item) {
      // 显示加载提示
      this.$message({
        message: `正在跳转到${item.title}...`,
        type: 'info',
        duration: 1000,
        showClose: false
      })

      // 添加点击反馈
      const event = new CustomEvent('nav-click', { detail: item })
      document.dispatchEvent(event)
    },
    handleGithubClick() {
      this.$message({
        message: '正在跳转到GitHub仓库...',
        type: 'success',
        duration: 2000
      })
    },
    toggleTheme() {
      this.isDark = !this.isDark
      document.documentElement.setAttribute('data-theme', this.isDark ? 'dark' : 'light')

      this.$message({
        message: `已切换到${this.isDark ? '深色' : '浅色'}主题`,
        type: 'success',
        duration: 1500
      })

      // 保存主题设置
      localStorage.setItem('theme', this.isDark ? 'dark' : 'light')
    },
    initTheme() {
      const savedTheme = localStorage.getItem('theme')
      if (savedTheme) {
        this.isDark = savedTheme === 'dark'
        document.documentElement.setAttribute('data-theme', savedTheme)
      }
    },
    getIconComponent(iconName) {
      // 如果是旧的 Element UI 图标名，转换为新的
      if (isOldElementIcon(iconName)) {
        return getNewIconName(iconName)
      }
      return iconName
    }
  },
  created() {
    this.initTheme()
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-main {
  padding: 0 !important;
}
.layout-container {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  background: var(--bg-secondary);
}

.gen-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 64px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-light);
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.scrolled {
    background: rgba(255, 255, 255, 0.98);
    box-shadow: var(--shadow-base);
  }

  .header-content {
    max-width: 1400px;
    margin: 0 auto;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    // padding: 0 var(--spacing-lg);

    @media (max-width: 768px) {
      padding: 0 var(--spacing-md);
    }
  }

  .logo-container {
    .logo-link {
      display: flex;
      align-items: center;
      text-decoration: none;
      color: var(--text-primary);
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      padding: var(--spacing-sm);
      border-radius: var(--border-radius-base);

      &:hover {
        transform: translateY(-2px);
        background: rgba(245, 112, 112, 0.05);

        .logo {
          transform: scale(1.1) rotate(5deg);
        }

        .logo-text {
          transform: translateX(2px);
        }
      }

      .logo {
        height: 40px;
        margin-right: var(--spacing-sm);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
      }

      .logo-text {
        font-size: 18px;
        font-weight: 600;
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        transition: all 0.3s ease;

        @media (max-width: 768px) {
          display: none;
        }
      }

      .logo-ripple {
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(245, 112, 112, 0.3);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: all 0.6s ease;

        &.active {
          width: 100px;
          height: 100px;
          opacity: 0;
        }
      }
    }
  }

  .nav-menu {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);

    .nav-item {
      display: flex;
      align-items: center;
      padding: var(--spacing-sm) var(--spacing-md);
      border-radius: var(--border-radius-base);
      text-decoration: none;
      color: var(--text-regular);
      font-weight: 500;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 0;
        left: -100%;
        width: 100%;
        height: 100%;
        background: linear-gradient(90deg, transparent, rgba(245, 112, 112, 0.1), transparent);
        transition: left 0.5s ease;
      }

      i {
        margin-right: var(--spacing-xs);
        font-size: 16px;
        transition: all 0.3s ease;
      }

      span {
        transition: all 0.3s ease;

        @media (max-width: 768px) {
          display: none;
        }
      }

      .nav-indicator {
        position: absolute;
        bottom: 0;
        left: 0;
        width: 0;
        height: 2px;
        background: var(--primary-gradient);
        transition: width 0.3s ease;
      }

      &:hover {
        color: var(--primary-color);
        background: rgba(245, 112, 112, 0.1);
        transform: translateY(-2px) scale(1.02);
        box-shadow: 0 4px 12px rgba(245, 112, 112, 0.2);

        &::before {
          left: 100%;
        }

        i {
          transform: scale(1.1);
        }

        .nav-indicator {
          width: 100%;
        }
      }

      &.active {
        color: var(--primary-color);
        background: rgba(245, 112, 112, 0.15);

        .nav-indicator {
          width: 100%;
        }

        &::after {
          content: '';
          position: absolute;
          bottom: -8px;
          left: 50%;
          transform: translateX(-50%);
          width: 20px;
          height: 2px;
          background: var(--primary-gradient);
          border-radius: 1px;
          animation: pulse 2s infinite;
        }
      }
    }
  }

  .header-actions {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);

    .action-btn {
      display: flex;
      align-items: center;
      justify-content: center;
      padding: var(--spacing-sm);
      border-radius: var(--border-radius-base);
      color: var(--text-regular);
      text-decoration: none;
      border: none;
      background: transparent;
      cursor: pointer;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      overflow: hidden;

      &::before {
        content: '';
        position: absolute;
        top: 50%;
        left: 50%;
        width: 0;
        height: 0;
        background: rgba(245, 112, 112, 0.2);
        border-radius: 50%;
        transform: translate(-50%, -50%);
        transition: all 0.3s ease;
      }

      .action-text {
        margin-left: var(--spacing-xs);
        font-size: 14px;
        font-weight: 500;

        @media (max-width: 768px) {
          display: none;
        }
      }

      &:hover {
        color: var(--primary-color);
        background: rgba(245, 112, 112, 0.1);
        transform: translateY(-2px) scale(1.05);
        box-shadow: 0 4px 12px rgba(245, 112, 112, 0.2);

        &::before {
          width: 40px;
          height: 40px;
        }
      }

      &:active {
        transform: translateY(-1px) scale(1.02);
      }

      &.theme-toggle {
        i {
          transition: all 0.3s ease;
        }

        &:hover i {
          transform: rotate(180deg);
        }
      }
    }
  }
}

.gen-main {
  flex: 1;
  // margin-top: 64px;
  min-height: calc(100vh - 120px);

  .main-content {
    // max-width: 1400px;
    margin: 0 auto;
  }
}

.gen-footer {
  background: var(--bg-primary);
  border-top: 1px solid var(--border-light);
  padding: var(--spacing-lg) 0;

  .footer-content {
    max-width: 1400px;
    margin: 0 auto;
    padding: 0 var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: center;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--spacing-md);
      padding: 0 var(--spacing-md);
    }

    .footer-info {
      color: var(--text-secondary);
      font-size: 14px;

      .footer-link {
        color: var(--primary-color);
        text-decoration: none;
        margin-left: var(--spacing-xs);
        transition: color 0.3s ease;

        &:hover {
          color: var(--primary-dark);
        }
      }
    }

    .footer-links {
      display: flex;
      gap: var(--spacing-lg);

      a {
        color: var(--text-secondary);
        text-decoration: none;
        font-size: 14px;
        transition: color 0.3s ease;

        &:hover {
          color: var(--primary-color);
        }
      }
    }
  }
}

.gen-footer {
  height: 40px;
  background: white;
  border-top: 1px solid #ebeef5;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;

  .footer-content {
    color: #666;
    font-size: 14px;

    a {
      color: #f57070;
      text-decoration: none;
      transition: color 0.3s ease;

      &:hover {
        color: #ff8e8e;
      }
    }
  }
}

@media (max-width: 768px) {
  .gen-header {
    .header-content {
      padding: 0 10px;
    }

    .logo-container {
      margin-right: 20px;

      .logo {
        height: 30px;
      }
    }

    .nav-menu {
      .el-menu-item {
        padding: 0 10px;
        font-size: 14px;

        i {
          font-size: 16px;
        }
      }
    }
  }
}

// 动画效果
@keyframes pulse {
  0%, 100% {
    opacity: 1;
    transform: translateX(-50%) scale(1);
  }
  50% {
    opacity: 0.7;
    transform: translateX(-50%) scale(1.1);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

// 页面过渡效果
:global(.page-transition) {
  .gen-main {
    animation: slideInUp 0.3s ease-out;
  }
}

// 深色主题支持
:global([data-theme="dark"]) {
  --bg-primary: #1a1a1a;
  --bg-secondary: #2d2d2d;
  --bg-tertiary: #3a3a3a;
  --text-primary: #ffffff;
  --text-regular: #e0e0e0;
  --text-secondary: #b0b0b0;
  --text-placeholder: #666666;
  --border-light: #404040;
  --border-base: #505050;
  --border-dark: #606060;

  .gen-header {
    background: rgba(26, 26, 26, 0.95);
    border-bottom-color: #404040;

    &.scrolled {
      background: rgba(26, 26, 26, 0.98);
    }
  }

  .gen-footer {
    background: #1a1a1a;
    border-top-color: #404040;
  }
}

// 响应式优化
@media (max-width: 768px) {
  .gen-header {
    .header-content {
      .nav-menu {
        .nav-item {
          padding: var(--spacing-sm);

          &:hover {
            transform: translateY(-1px) scale(1.05);
          }
        }
      }

      .header-actions {
        .action-btn {
          padding: var(--spacing-xs);

          &:hover {
            transform: translateY(-1px) scale(1.1);
          }
        }
      }
    }
  }
}

// 无障碍访问优化
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }
}

// 高对比度模式
@media (prefers-contrast: high) {
  .gen-header {
    border-bottom-width: 2px;

    .nav-item {
      &.active::after {
        height: 3px;
      }
    }
  }
}
</style>
