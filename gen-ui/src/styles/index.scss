// 设计系统变量
:root {
  // 主色调
  --primary-color: #f57070;
  --primary-light: #ff8e8e;
  --primary-dark: #e55a5a;
  --primary-gradient: linear-gradient(135deg, #f57070 0%, #ff8e8e 100%);

  // 辅助色
  --success-color: #67c23a;
  --warning-color: #e6a23c;
  --danger-color: #f56c6c;
  --info-color: #909399;

  // 文字颜色
  --text-primary: #303133;
  --text-regular: #606266;
  --text-secondary: #909399;
  --text-placeholder: #c0c4cc;

  // 边框颜色
  --border-light: #ebeef5;
  --border-base: #dcdfe6;
  --border-dark: #d4d7de;

  // 背景色
  --bg-primary: #ffffff;
  --bg-secondary: #f5f7fa;
  --bg-tertiary: #fafbfc;

  // 阴影
  --shadow-light: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
  --shadow-base: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
  --shadow-dark: 0 4px 8px rgba(0, 0, 0, 0.12), 0 2px 4px rgba(0, 0, 0, 0.08);

  // 圆角
  --border-radius-small: 4px;
  --border-radius-base: 8px;
  --border-radius-large: 12px;

  // 间距
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 32px;
}

body {
  height: 100%;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, 'Noto Sans', sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol', 'Noto Color Emoji';
  background-color: var(--bg-secondary);
  color: var(--text-primary);
  line-height: 1.6;
}

.app-container {
  padding: var(--spacing-xl);
  max-width: 1400px;
  margin: 0 auto;

  @media (max-width: 768px) {
    padding: var(--spacing-md);
  }
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

// 通用组件样式
.gen-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 64px;
  background: var(--bg-primary);
  backdrop-filter: blur(10px);
  border-bottom: 1px solid var(--border-light);
  z-index: 1000;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.scrolled {
    box-shadow: var(--shadow-base);
  }
}

.gen-main {
  flex: 1;
  margin-top: 64px;
  //padding: var(--spacing-lg);
  background: var(--bg-secondary);
  min-height: calc(100vh - 120px);
}

.gen-footer {
  text-align: center;
  color: var(--text-secondary);
  padding: var(--spacing-lg);
  background: var(--bg-primary);
  border-top: 1px solid var(--border-light);

  a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color 0.3s ease;

    &:hover {
      color: var(--primary-dark);
    }
  }
}

// Element Plus 组件样式覆盖
.el-card {
  border-radius: var(--border-radius-large);
  border: 1px solid var(--border-light);
  box-shadow: var(--shadow-light);
  transition: all 0.3s ease;

  &:hover {
    box-shadow: var(--shadow-base);
  }

  .el-card__header {
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-light);
    padding: var(--spacing-md) var(--spacing-lg);
  }

  .el-card__body {
    padding: var(--spacing-lg);
  }
}

.el-dialog {
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-dark);

  .el-dialog__header {
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-light);
    border-radius: var(--border-radius-large) var(--border-radius-large) 0 0;
  }

  .el-dialog__body {
    padding: var(--spacing-lg);
  }
}

.el-message-box {
  border-radius: var(--border-radius-large);
  box-shadow: var(--shadow-dark);
}

// 通用工具类
.mt-xs { margin-top: var(--spacing-xs); }
.mt-sm { margin-top: var(--spacing-sm); }
.mt-md { margin-top: var(--spacing-md); }
.mt-lg { margin-top: var(--spacing-lg); }
.mt-xl { margin-top: var(--spacing-xl); }

.mb-xs { margin-bottom: var(--spacing-xs); }
.mb-sm { margin-bottom: var(--spacing-sm); }
.mb-md { margin-bottom: var(--spacing-md); }
.mb-lg { margin-bottom: var(--spacing-lg); }
.mb-xl { margin-bottom: var(--spacing-xl); }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

// 按钮组件增强
.el-button {
  border-radius: var(--border-radius-base);
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &.el-button--primary {
    background: var(--primary-gradient);
    border: none;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(245, 112, 112, 0.3);
    }
  }

  &.el-button--text {
    color: var(--primary-color);

    &:hover {
      background: rgba(245, 112, 112, 0.1);
    }
  }
}

// 表格组件增强
.el-table {
  border-radius: var(--border-radius-base);
  overflow: hidden;
  box-shadow: var(--shadow-light);

  .el-table__header {
    background: var(--bg-tertiary);

    th {
      background: var(--bg-tertiary);
      color: var(--text-primary);
      font-weight: 600;
      border-bottom: 2px solid var(--border-light);
    }
  }

  .el-table__row {
    transition: background-color 0.3s ease;

    &:hover {
      background: rgba(245, 112, 112, 0.05);
    }
  }
}

// 表单组件增强
.el-form-item {
  margin-bottom: var(--spacing-lg);

  .el-form-item__label {
    color: var(--text-primary);
    font-weight: 500;
    line-height: 32px;
    padding-bottom: 0;
  }
  
  .el-form-item__content {
    position: relative;
    line-height: 32px;
  }
}

// Element Plus 输入框样式修复
.el-input {
  width: 100%;

  .el-input__wrapper {
    border-radius: var(--border-radius-base);
    transition: all 0.3s ease;
    box-shadow: 0 0 0 1px var(--border-base) inset;
    border: none;
    background-color: var(--bg-primary);

    &:hover {
      box-shadow: 0 0 0 1px var(--primary-color) inset;
    }

    &.is-focus {
      box-shadow: 0 0 0 1px var(--primary-color) inset;
    }

    &.is-disabled {
      background-color: var(--bg-tertiary);
      box-shadow: 0 0 0 1px var(--border-light) inset;
    }
  }

  .el-input__inner {
    color: var(--text-primary);
    background: transparent;
    border: none;
    box-shadow: none;

    &::placeholder {
      color: var(--text-placeholder);
    }
  }
}

.el-textarea {
  .el-textarea__inner {
    border-radius: var(--border-radius-base);
    transition: all 0.3s ease;
    resize: vertical;
    border: 1px solid var(--border-base);
    background-color: var(--bg-primary);

    &:hover {
      border-color: var(--primary-color);
    }

    &:focus {
      border-color: var(--primary-color);
      box-shadow: 0 0 0 2px rgba(245, 112, 112, 0.2);
      outline: none;
    }

    &::placeholder {
      color: var(--text-placeholder);
    }
  }
}

.el-select {
  width: 100%;

  .el-select__wrapper {
    border-radius: var(--border-radius-base);
    transition: all 0.3s ease;
    box-shadow: 0 0 0 1px var(--border-base) inset;
    border: none;
    background-color: var(--bg-primary);

    &:hover {
      box-shadow: 0 0 0 1px var(--primary-color) inset;
    }

    &.is-focus {
      box-shadow: 0 0 0 1px var(--primary-color) inset;
    }

    &.is-disabled {
      background-color: var(--bg-tertiary);
      box-shadow: 0 0 0 1px var(--border-light) inset;
    }
  }
}

// 日期选择器样式修复
.el-date-editor {
  .el-input__wrapper {
    border-radius: var(--border-radius-base);
    transition: all 0.3s ease;
    box-shadow: 0 0 0 1px var(--border-base) inset;
    border: none;
    background-color: var(--bg-primary);

    &:hover {
      box-shadow: 0 0 0 1px var(--primary-color) inset;
    }

    &.is-focus {
      box-shadow: 0 0 0 1px var(--primary-color) inset;
    }
  }
}

// 按钮组样式增强
.el-button-group {
  .el-button {
    &:first-child {
      border-top-left-radius: var(--border-radius-base);
      border-bottom-left-radius: var(--border-radius-base);
    }

    &:last-child {
      border-top-right-radius: var(--border-radius-base);
      border-bottom-right-radius: var(--border-radius-base);
    }

    &:not(:first-child):not(:last-child) {
      border-radius: 0;
    }
  }
}

// 下拉菜单样式增强
.el-dropdown-menu {
  border-radius: var(--border-radius-base);
  box-shadow: var(--shadow-dark);
  border: 1px solid var(--border-light);

  .el-dropdown-menu__item {
    transition: all 0.3s ease;

    &:hover {
      background: rgba(245, 112, 112, 0.1);
      color: var(--primary-color);
    }
  }
}

// 标签样式增强
.el-tag {
  border-radius: var(--border-radius-small);
  font-weight: 500;

  &.el-tag--primary {
    background: rgba(245, 112, 112, 0.1);
    border-color: var(--primary-color);
    color: var(--primary-color);
  }
}

// 工具提示样式增强
.el-tooltip__popper {
  border-radius: var(--border-radius-base);
  box-shadow: var(--shadow-dark);
}

// 分页组件样式增强
.el-pagination {
  .el-pager li {
    border-radius: var(--border-radius-small);
    transition: all 0.3s ease;

    &.is-active {
      background: var(--primary-gradient);
      color: white;
    }

    &:hover:not(.is-active) {
      background: rgba(245, 112, 112, 0.1);
      color: var(--primary-color);
    }
  }

  .btn-prev,
  .btn-next {
    border-radius: var(--border-radius-small);
    transition: all 0.3s ease;

    &:hover {
      background: rgba(245, 112, 112, 0.1);
      color: var(--primary-color);
    }
  }
}

// 加载组件样式增强
.el-loading-mask {
  background-color: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(4px);
}

// 消息组件样式增强
.el-message {
  border-radius: var(--border-radius-base);
  box-shadow: var(--shadow-dark);
  backdrop-filter: blur(10px);
}

.el-notification {
  border-radius: var(--border-radius-base);
  box-shadow: var(--shadow-dark);
  backdrop-filter: blur(10px);
}

// 动画类
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes fadeInDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

.fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.fade-in-down {
  animation: fadeInDown 0.6s ease-out;
}

.slide-in-right {
  animation: slideInRight 0.6s ease-out;
}

// 用户交互体验增强
// 滚动条美化
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-tertiary);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(45deg, var(--primary-color), var(--primary-light));
  border-radius: 4px;
  transition: all 0.3s ease;

  &:hover {
    background: linear-gradient(45deg, var(--primary-dark), var(--primary-color));
    transform: scale(1.1);
  }
}

// 选择文本样式
::selection {
  background: rgba(245, 112, 112, 0.2);
  color: var(--text-primary);
}

// 焦点样式增强
:focus-visible {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
  border-radius: var(--border-radius-small);
}

// 鼠标悬停效果增强
.hover-lift {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-base);
  }
}

.hover-scale {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);

  &:hover {
    transform: scale(1.05);
  }
}

.hover-glow {
  position: relative;
  transition: all 0.3s ease;

  &::before {
    content: '';
    position: absolute;
    inset: -2px;
    background: var(--primary-gradient);
    border-radius: inherit;
    opacity: 0;
    z-index: -1;
    filter: blur(6px);
    transition: opacity 0.3s ease;
  }

  &:hover::before {
    opacity: 0.3;
  }
}

// 点击反馈效果
.click-effect {
  position: relative;
  overflow: hidden;

  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.5);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: width 0.6s, height 0.6s;
  }

  &:active::after {
    width: 300px;
    height: 300px;
  }
}

// 加载状态动画
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

.loading-skeleton {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200px 100%;
  animation: shimmer 1.5s infinite;
}

.pulse {
  animation: pulse 2s infinite;
}

// 通知和提示样式增强
.notification-enter-active,
.notification-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.notification-enter {
  opacity: 0;
  transform: translateX(100%);
}

.notification-leave-to {
  opacity: 0;
  transform: translateX(100%);
}

// 性能优化相关样式
.performance-optimized {
  will-change: transform;
  transform: translateZ(0);
}

// 响应式优化
@media (max-width: 768px) {
  .el-card {
    margin: 10px;

    .el-card__body {
      padding: 15px;
    }
  }

  .el-table {
    font-size: 12px;
  }

  .el-button {
    padding: 8px 15px;
    font-size: 12px;
  }

  .toolbar-section {
    .el-button-group {
      flex-direction: column;

      .el-button {
        margin-bottom: 8px;
        border-radius: var(--border-radius-base) !important;
      }
    }
  }
}

// 打印样式优化
@media print {
  .no-print {
    display: none !important;
  }

  .el-card {
    box-shadow: none;
    border: 1px solid #ddd;
    break-inside: avoid;
  }

  .el-table {
    border: 1px solid #ddd;

    th, td {
      border: 1px solid #ddd;
      padding: 8px;
    }
  }

  .page-break {
    page-break-before: always;
  }
}

// 高对比度模式支持
@media (prefers-contrast: high) {
  :root {
    --primary-color: #000;
    --text-primary: #000;
    --bg-primary: #fff;
    --border-base: #000;
  }

  .el-button--primary {
    background: #000;
    border-color: #000;
    color: #fff;
  }
}

// 减少动画模式支持
@media (prefers-reduced-motion: reduce) {
  * {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
  }

  .loading-skeleton {
    animation: none;
    background: #f0f0f0;
  }
}

// 暗色主题支持
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #1a1a1a;
    --bg-secondary: #2d2d2d;
    --bg-tertiary: #404040;
    --text-primary: #ffffff;
    --text-secondary: #cccccc;
    --text-placeholder: #999999;
    --border-base: #404040;
    --border-light: #333333;
    --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.3);
    --shadow-base: 0 4px 16px rgba(0, 0, 0, 0.4);
    --shadow-dark: 0 8px 32px rgba(0, 0, 0, 0.5);
  }

  .el-card {
    background: var(--bg-secondary);
    border-color: var(--border-base);
  }

  .el-table {
    background: var(--bg-secondary);
    color: var(--text-primary);

    th {
      background: var(--bg-tertiary);
      color: var(--text-primary);
    }

    tr:hover {
      background: var(--bg-tertiary);
    }
  }
}

