<template>
  <div class="changelog-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <h1 class="page-title">
          <el-icon class="title-icon"><Document /></el-icon>
          更新日志
          <span class="subtitle">Changelog</span>
        </h1>
        <p class="page-description">
          记录系统的重要更新和功能改进
        </p>
      </div>
    </div>

    <!-- 更新日志内容 -->
    <div class="changelog-content">
      <el-timeline>
        <!-- 版本 2.1.0 -->
        <el-timeline-item
          timestamp="2025-07-01"
          placement="top"
          type="primary"
          size="large"
          icon="Star"
        >
          <el-card class="version-card">
            <template #header>
              <div class="version-header">
                <span class="version-number">v2.1.0</span>
                <el-tag type="success">最新版本</el-tag>
              </div>
            </template>
            
            <div class="update-content">
              <h3>🎉 重大功能更新</h3>
              <ul class="feature-list">
                <li>
                  <el-icon class="feature-icon success"><Check /></el-icon>
                  <strong>智能代码工厂</strong> - 全新的一键生成完整项目功能
                </li>
                <li>
                  <el-icon class="feature-icon success"><Check /></el-icon>
                  <strong>Vue3升级</strong> - 前端框架升级到Vue3，性能大幅提升
                </li>
                <li>
                  <el-icon class="feature-icon success"><Check /></el-icon>
                  <strong>包名修复</strong> - 修复生成项目的包名和导入问题
                </li>
                <li>
                  <el-icon class="feature-icon success"><Check /></el-icon>
                  <strong>界面优化</strong> - 全面优化用户界面，提升操作体验
                </li>
              </ul>

              <h3>🔧 问题修复</h3>
              <ul class="fix-list">
                <li>修复数据表管理删除按钮缺失问题</li>
                <li>修复一键生成按钮无响应问题</li>
                <li>修复表设置页面字段显示问题</li>
                <li>优化重复请求限制策略</li>
                <li>修复MySQL 8.0连接兼容性问题</li>
              </ul>

              <h3>⚡ 性能优化</h3>
              <ul class="performance-list">
                <li>优化项目生成速度，提升50%效率</li>
                <li>改进前端加载性能</li>
                <li>优化数据库连接池配置</li>
                <li>减少不必要的网络请求</li>
              </ul>
            </div>
          </el-card>
        </el-timeline-item>

        <!-- 版本 2.0.5 -->
        <el-timeline-item
          timestamp="2025-06-15"
          placement="top"
          type="warning"
          icon="Tools"
        >
          <el-card class="version-card">
            <template #header>
              <div class="version-header">
                <span class="version-number">v2.0.5</span>
                <el-tag type="warning">稳定版本</el-tag>
              </div>
            </template>
            
            <div class="update-content">
              <h3>🔧 功能改进</h3>
              <ul class="feature-list">
                <li>
                  <el-icon class="feature-icon warning"><Tools /></el-icon>
                  增强代码生成模板的灵活性
                </li>
                <li>
                  <el-icon class="feature-icon warning"><Tools /></el-icon>
                  改进数据库表结构同步功能
                </li>
                <li>
                  <el-icon class="feature-icon warning"><Tools /></el-icon>
                  优化批量操作性能
                </li>
              </ul>

              <h3>🐛 Bug修复</h3>
              <ul class="fix-list">
                <li>修复特殊字符表名处理问题</li>
                <li>修复代码预览格式化问题</li>
                <li>修复导出功能在某些浏览器下的兼容性</li>
              </ul>
            </div>
          </el-card>
        </el-timeline-item>

        <!-- 版本 2.0.0 -->
        <el-timeline-item
          timestamp="2025-05-01"
          placement="top"
          type="info"
          icon="Rocket"
        >
          <el-card class="version-card">
            <template #header>
              <div class="version-header">
                <span class="version-number">v2.0.0</span>
                <el-tag type="info">里程碑版本</el-tag>
              </div>
            </template>
            
            <div class="update-content">
              <h3>🚀 重大版本发布</h3>
              <ul class="feature-list">
                <li>
                  <el-icon class="feature-icon info"><Rocket /></el-icon>
                  全新的代码生成引擎
                </li>
                <li>
                  <el-icon class="feature-icon info"><Rocket /></el-icon>
                  支持多种前端框架模板
                </li>
                <li>
                  <el-icon class="feature-icon info"><Rocket /></el-icon>
                  引入MyBatis Plus支持
                </li>
                <li>
                  <el-icon class="feature-icon info"><Rocket /></el-icon>
                  全新的用户界面设计
                </li>
              </ul>
            </div>
          </el-card>
        </el-timeline-item>

        <!-- 版本 1.5.0 -->
        <el-timeline-item
          timestamp="2025-03-15"
          placement="top"
          type="success"
          icon="CircleCheck"
        >
          <el-card class="version-card">
            <template #header>
              <div class="version-header">
                <span class="version-number">v1.5.0</span>
                <el-tag>历史版本</el-tag>
              </div>
            </template>
            
            <div class="update-content">
              <h3>✨ 功能增强</h3>
              <ul class="feature-list">
                <li>
                  <el-icon class="feature-icon"><CircleCheck /></el-icon>
                  添加数据库文档导出功能
                </li>
                <li>
                  <el-icon class="feature-icon"><CircleCheck /></el-icon>
                  支持自定义代码模板
                </li>
                <li>
                  <el-icon class="feature-icon"><CircleCheck /></el-icon>
                  增加代码预览功能
                </li>
              </ul>
            </div>
          </el-card>
        </el-timeline-item>
      </el-timeline>
    </div>

    <!-- 页面底部 -->
    <div class="page-footer">
      <el-divider>
        <el-icon><Star /></el-icon>
        感谢您的使用
        <el-icon><Star /></el-icon>
      </el-divider>
      <p class="footer-text">
        如有问题或建议，请联系开发团队
      </p>
    </div>
  </div>
</template>

<script setup>
import { 
  Document, 
  Star, 
  Check, 
  Tools, 
  Rocket, 
  CircleCheck 
} from '@element-plus/icons-vue'
</script>

<style scoped>
.changelog-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.page-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.page-title {
  font-size: 2.5rem;
  font-weight: 700;
  color: #2c3e50;
  margin: 0 0 16px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
}

.title-icon {
  font-size: 2.5rem;
  color: #3498db;
}

.subtitle {
  font-size: 1.2rem;
  font-weight: 400;
  color: #7f8c8d;
  margin-left: 8px;
}

.page-description {
  font-size: 1.1rem;
  color: #7f8c8d;
  margin: 0;
  line-height: 1.6;
}

.changelog-content {
  background: white;
  border-radius: 16px;
  padding: 40px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 40px;
}

.version-card {
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.version-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.version-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.version-number {
  font-size: 1.5rem;
  font-weight: 700;
  color: #2c3e50;
}

.update-content h3 {
  color: #2c3e50;
  font-size: 1.2rem;
  font-weight: 600;
  margin: 20px 0 12px 0;
  display: flex;
  align-items: center;
  gap: 8px;
}

.feature-list,
.fix-list,
.performance-list {
  list-style: none;
  padding: 0;
  margin: 0 0 20px 0;
}

.feature-list li,
.fix-list li,
.performance-list li {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px 0;
  border-bottom: 1px solid #f8f9fa;
}

.feature-list li:last-child,
.fix-list li:last-child,
.performance-list li:last-child {
  border-bottom: none;
}

.feature-icon {
  margin-top: 2px;
  flex-shrink: 0;
}

.feature-icon.success {
  color: #27ae60;
}

.feature-icon.warning {
  color: #f39c12;
}

.feature-icon.info {
  color: #3498db;
}

.fix-list li::before {
  content: "🐛";
  margin-right: 8px;
  flex-shrink: 0;
}

.performance-list li::before {
  content: "⚡";
  margin-right: 8px;
  flex-shrink: 0;
}

.page-footer {
  text-align: center;
  padding: 20px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.footer-text {
  color: #7f8c8d;
  margin: 0;
  font-size: 0.9rem;
}

/* 时间线样式优化 */
:deep(.el-timeline-item__timestamp) {
  font-weight: 600;
  color: #3498db;
}

:deep(.el-timeline-item__wrapper) {
  padding-left: 40px;
}

:deep(.el-timeline-item__tail) {
  border-left: 2px solid #e1e8ed;
}

:deep(.el-timeline-item__node) {
  width: 16px;
  height: 16px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .changelog-container {
    padding: 10px;
  }
  
  .page-title {
    font-size: 2rem;
    flex-direction: column;
    gap: 8px;
  }
  
  .changelog-content {
    padding: 20px;
  }
  
  :deep(.el-timeline-item__wrapper) {
    padding-left: 20px;
  }
}
</style>
