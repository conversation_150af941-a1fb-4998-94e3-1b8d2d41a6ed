<template>
  <div class="datasource-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-background">
        <div class="header-pattern"></div>
      </div>
      <div class="header-content">
        <div class="header-info">
          <div class="page-breadcrumb">
            <span class="breadcrumb-item">工作台</span>
            <el-icon><ArrowRight /></el-icon>
            <span class="breadcrumb-item active">数据源管理</span>
          </div>
          <h1 class="page-title">
            <div class="title-icon">
              <el-icon><Connection /></el-icon>
            </div>
            <span>数据源管理</span>
          </h1>
          <p class="page-description">统一管理数据库连接配置，支持多种数据库类型，确保数据连接的安全性和稳定性</p>
        </div>
        <div class="header-stats">
          <div class="stat-card primary">
            <div class="stat-icon">
              <el-icon><Grid /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ total }}</div>
              <div class="stat-label">数据源总数</div>
            </div>
          </div>
          <div class="stat-card success">
            <div class="stat-icon">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ activeCount }}</div>
              <div class="stat-label">活跃连接</div>
            </div>
          </div>
          <div class="stat-card warning">
            <div class="stat-icon">
              <el-icon><Platform /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ dbTypeCount }}</div>
              <div class="stat-label">数据库类型</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section" v-show="showSearch">
      <el-card class="search-card">
        <div class="search-header">
          <div class="search-title">
            <el-icon><Search /></el-icon>
            <span>筛选条件</span>
          </div>
          <el-button type="text" @click="showSearch = false" class="close-search">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
        <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
          <div class="form-row">
            <el-form-item label="数据源名称" prop="name" class="form-item">
              <el-input
                v-model="queryParams.name"
                placeholder="请输入数据源名称"
                clearable
                :prefix-icon="Grid"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="数据库名称" prop="dbName" class="form-item">
              <el-input
                v-model="queryParams.dbName"
                placeholder="请输入数据库名称"
                clearable
                :prefix-icon="Coin"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="数据库类型" prop="type" class="form-item">
              <el-select v-model="queryParams.type" placeholder="请选择数据库类型" clearable>
                <el-option label="MySQL" value="0" />
                <el-option label="Oracle" value="1" />
                <el-option label="SQL Server" value="2" />
                <el-option label="PostgreSQL" value="3" />
                <el-option label="ClickHouse" value="4" />
                <el-option label="SQLite" value="5" />
                <el-option label="DB2" value="6" />
                <el-option label="达梦" value="7" />
              </el-select>
            </el-form-item>
          </div>
          <div class="form-actions">
            <el-button type="primary" @click="handleQuery">
          <template #icon><el-icon><Search /></el-icon></template>
              搜索数据源
            </el-button>
            <el-button @click="resetQuery">
          <template #icon><el-icon><Refresh /></el-icon></template>
              重置条件
            </el-button>
          </div>
        </el-form>
      </el-card>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar-section">
      <el-card class="toolbar-card">
        <div class="toolbar-header">
          <div class="toolbar-title">
            <el-icon><Setting /></el-icon>
            <span>操作面板</span>
          </div>
          <div class="selection-info" v-if="ids.length > 0">
            <el-tag type="primary" size="small">已选择 {{ ids.length }} 个数据源</el-tag>
          </div>
        </div>

        <div class="toolbar-content">
          <div class="primary-actions">
            <el-button
              type="primary"
              @click="handleAdd"
              size="medium"
              class="primary-btn"
            >
              <template #icon><el-icon><Plus /></el-icon></template>
              新增数据源
            </el-button>

            <el-button
              type="success"
              :disabled="single"
              @click="handleUpdate"
              size="medium"
              class="secondary-btn"
            >
              <template #icon><el-icon><Edit /></el-icon></template>
              修改配置
            </el-button>

            <el-button
              type="danger"
              :disabled="multiple"
              @click="handleDelete"
              size="medium"
              class="secondary-btn"
            >
              <template #icon><el-icon><Delete /></el-icon></template>
              删除数据源
            </el-button>


          </div>

          <div class="secondary-actions">
            <div class="toolbar-tools">
              <el-tooltip content="筛选搜索" placement="top">
                <el-button
                  circle
                  @click="showSearch = !showSearch"
                  :type="showSearch ? 'primary' : ''"
                  size="medium"
                >
                  <template #icon><el-icon><Search /></el-icon></template>
                </el-button>
              </el-tooltip>
              <el-tooltip content="刷新数据" placement="top">
                <el-button circle @click="getList" size="medium">
          <template #icon><el-icon><Refresh /></el-icon></template>
        </el-button>
              </el-tooltip>
              <el-tooltip content="连接测试" placement="top">
                <el-button circle size="medium">
          <template #icon><el-icon><Link /></el-icon></template>
        </el-button>
              </el-tooltip>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 数据表格 -->
    <div class="table-section">
      <el-card class="table-card">
        <div class="table-header">
          <div class="table-title">
            <div class="title-content">
              <el-icon><Grid /></el-icon>
              <span>数据源列表</span>
              <el-tag v-if="total > 0" type="info" size="mini" class="total-tag">
                共 {{ total }} 个数据源
              </el-tag>
            </div>
          </div>
        </div>

        <el-table
          v-loading="loading"
          :data="datasourceList"
          @selection-change="handleSelectionChange"
          class="datasource-table"
          :empty-text="loading ? '加载中...' : '暂无数据源'"
        >
          <el-table-column type="selection" width="50" align="center" />

          <el-table-column label="序号" width="60" align="center">
            <template #default="scope">
              <span class="table-index">{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>

          <el-table-column label="数据源信息" min-width="250">
            <template #default="scope">
              <div class="datasource-info">
                <div class="datasource-name">
                  <div class="name-wrapper">
                    <el-icon class="datasource-icon"><Grid /></el-icon>
                    <span class="name-text">{{ scope.row.name }}</span>
                  </div>
                </div>
                <div class="datasource-details">
                  <div class="detail-item">
                    <el-icon><Link /></el-icon>
                    <span>{{ scope.row.url }}:{{ scope.row.port }}</span>
                  </div>
                  <div class="detail-item">
                    <el-icon><Coin /></el-icon>
                    <span>{{ scope.row.dbName }}</span>
                  </div>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="数据库类型" width="120" align="center">
            <template #default="scope">
              <div class="db-type">
                <el-tag
                  :type="getDbTypeColor(scope.row.type)"
                  size="small"
                  class="db-tag"
                >
                  <el-icon><component :is="getDbTypeIconComponent(scope.row.type)" /></el-icon>
                  {{ getDbTypeName(scope.row.type) }}
                </el-tag>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="数据库类型" width="120" align="center">
            <template #default="scope">
              <el-tag
                :type="scope.row.status === 1 ? 'success' : 'info'"
                size="mini"
                class="status-tag"
              >
                {{ scope.row.status === 1 ? '默认' : '正常' }}
              </el-tag>
            </template>
          </el-table-column>

          <el-table-column label="认证信息" width="180" align="center">
            <template #default="scope">
              <div class="auth-info">
                <div class="username">
                  <el-icon><User /></el-icon>
                  {{ scope.row.username }}
                </div>
                <div class="password">
                  <el-icon><Lock /></el-icon>
                  <span>******</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="200" align="center" fixed="right">
            <template #default="scope">
              <div class="table-actions">
                <el-button-group class="action-group">
                  <el-tooltip content="测试连接" placement="top">
                    <el-button
                      type="primary"
                      size="small"
                      @click="testConnection(scope.row)"
                      class="action-btn test-btn"
                    >
                      <template #icon><el-icon><Link /></el-icon></template>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="切换数据源" placement="top">
                    <el-button
                      type="warning"
                      size="small"
                      @click="switchDataSource(scope.row)"
                      class="action-btn switch-btn"
                    >
                      <template #icon><el-icon><Refresh /></el-icon></template>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="编辑配置" placement="top">
                    <el-button
                      type="success"
                      size="small"
                      @click="handleUpdate(scope.row)"
                      class="action-btn edit-btn"
                    >
                      <template #icon><el-icon><Edit /></el-icon></template>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="删除数据源" placement="top">
                    <el-button
                      type="danger"
                      size="small"
                      @click="handleDelete(scope.row)"
                      class="action-btn delete-btn"
                    >
                      <template #icon><el-icon><Delete /></el-icon></template>
                    </el-button>
                  </el-tooltip>
                </el-button-group>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <pagination
            v-show="total > 0"
            :total="total"
            :page.sync="queryParams.pageNum"
            :limit.sync="queryParams.pageSize"
            @pagination="handlePagination"
            layout="total, sizes, prev, pager, next, jumper"
          />
        </div>
      </el-card>
    </div>





    <!-- 添加或修改动态数据源配置对话框 -->
    <el-dialog :title="title" v-model="open" width="800px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="98px">
        <el-form-item label="数据源名称" prop="name">
          <el-input v-model="form.name" placeholder="请输入数据源名称" />
        </el-form-item>
        <el-form-item label="连接地址" prop="url">
          <el-input v-model="form.url" placeholder="请输入数据库连接地址" />
        </el-form-item>
        <el-form-item label="端口号" prop="port">
          <el-input v-model="form.port" placeholder="请输入数据库端口号" />
        </el-form-item>
        <el-form-item label="数据库名称" prop="dbName">
          <el-input v-model="form.dbName" placeholder="请输入数据库名称" />
        </el-form-item>
        <el-form-item label="用户名" prop="username">
          <el-input v-model="form.username" placeholder="请输入数据库用户名" />
        </el-form-item>
        <el-form-item label="密码" prop="password">
          <el-input v-model="form.password" type="password" placeholder="请输入数据库密码" show-password />
        </el-form-item>
<!--        <el-form-item label="驱动类名" prop="driverClassName">-->
<!--          <el-input v-model="form.driverClassName" placeholder="请输入数据库驱动类名" />-->
<!--        </el-form-item>-->
        <el-form-item label="数据库类型" prop="type">
          <el-select v-model="form.type" placeholder="请选择数据库类型" clearable>
            <el-option label="MySQL" value="0" />
            <el-option label="Oracle" value="1" />
            <el-option label="sqlserver" value="2" />
            <el-option label="postgresql" value="3" />
            <el-option label="clickhouse" value="4" />
            <el-option label="sqlite" value="5" />
            <el-option label="db2" value="6" />
            <el-option label="dm" value="7" />
          </el-select>
        </el-form-item>
        <el-form-item label="数据源状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio label="1">默认</el-radio>
            <el-radio label="2">正常</el-radio>
          </el-radio-group>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import dataSourceApi from "@/api/dataSource";
import genApi from "@/api/gen";
import IconHelper from "@/utils/icon-helper";
import {
  ArrowRight,
  Connection,
  Grid,
  Check,
  Platform,
  Search,
  Close,
  Coin,
  Refresh,
  Setting,
  Plus,
  Edit,
  Delete,
  Link,
  User,
  Lock,
  Files,
  TrendCharts
} from '@element-plus/icons-vue';
export default {
  name: "Datasource",
  setup() {
    return {
      $iconHelper: IconHelper
    }
  },
  components: {
    ArrowRight,
    Connection,
    Grid,
    Check,
    Platform,
    Search,
    Close,
    Coin,
    Refresh,
    Setting,
    Plus,
    Edit,
    Delete,
    Link,
    User,
    Lock,
    Files,
    TrendCharts
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 动态数据源配置表格数据
      datasourceList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        name: null,
        url: null,
        port: null,
        dbName: null,
        username: null,
        password: null,
        driverClassName: null,
        type: null,
        status: null,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        name: [
          { required: true, message: "数据源名称不能为空", trigger: "blur" }
        ],        url: [
          { required: true, message: "数据库连接地址不能为空", trigger: "blur" }
        ],        port: [
          { required: true, message: "数据库端口号不能为空", trigger: "blur" }
        ],        dbName: [
          { required: true, message: "数据库名称不能为空", trigger: "blur" }
        ],        username: [
          { required: true, message: "数据库用户名不能为空", trigger: "blur" }
        ],        password: [
          { required: true, message: "数据库密码不能为空", trigger: "blur" }
        ],        driverClassName: [
          { required: true, message: "数据库驱动类名不能为空", trigger: "blur" }
        ],
      },

      // 预览相关
      previewDialogVisible: false,
      previewLoading: false,
      previewTableList: [],
      selectedPreviewTables: [],
      currentDataSource: {},

      // 导出相关
      exportDialogVisible: false,
      exporting: false,
      exportConfig: {
        exportType: 'WORD',
        exportScope: 'selected'
      }
    };
  },
  computed: {
    activeCount() {
      return this.datasourceList.filter(item => item.status === 1).length;
    },
    dbTypeCount() {
      const types = new Set(this.datasourceList.map(item => item.type));
      return types.size;
    }
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询动态数据源配置列表 */
    getList() {
      this.loading = true;
      dataSourceApi.listDatasource(this.queryParams).then(response => {
        this.datasourceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        id: null,
        name: null,
        url: null,
        port: null,
        dbName: null,
        username: null,
        password: null,
        driverClassName: null,
        type: null,
        status: null,
        createTime: null,
        updateTime: null
      };
    },
    /** 分页处理 */
    handlePagination(pagination) {
      console.log('数据源分页事件:', pagination);
      this.queryParams.pageNum = pagination.page;
      this.queryParams.pageSize = pagination.limit;
      this.getList();
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.reset();
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.id)
      this.single = selection.length!==1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加动态数据源配置";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const id = row.id || this.ids
      dataSourceApi.getDatasource(id).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改动态数据源配置";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.id != null) {
            dataSourceApi.updateDatasource(this.form).then(response => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            dataSourceApi.addDatasource(this.form).then(response => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const ids = row.id || this.ids;
      this.$modal.confirm('是否确认删除动态数据源配置编号为"' + ids + '"的数据项？').then(function() {
        return dataSourceApi.delDatasource(ids);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('javaxiaobear/datasource/export', {
        ...this.queryParams
      }, `datasource_${new Date().getTime()}.xlsx`)
    },
    /** 测试连接 */
    testConnection(row) {
      this.$message({
        message: `正在测试 ${row.name} 的连接...`,
        type: 'info',
        duration: 2000
      });

      dataSourceApi.testConnection(row).then(response => {
        this.$message.success(response.msg);
      }).catch(error => {
        this.$message.error('连接测试失败：' + error.message);
      });
    },
    /** 切换数据源 */
    switchDataSource(row) {
      this.$confirm(`确认切换到数据源 "${row.name}" 吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        dataSourceApi.switchDataSource(row.id).then(response => {
          this.$message.success(response.msg);
          // 可以在这里触发其他页面的数据刷新
          this.$emit('dataSourceChanged', row);
        }).catch(error => {
          this.$message.error('数据源切换失败：' + error.message);
        });
      });
    },
    /** 获取数据库类型名称 */
    getDbTypeName(type) {
      const names = {
        '0': 'MySQL',
        '1': 'Oracle',
        '2': 'SQL Server',
        '3': 'PostgreSQL',
        '4': 'ClickHouse',
        '5': 'SQLite',
        '6': 'DB2',
        '7': '达梦'
      };
      return names[type] || 'Unknown';
    },
    /** 获取数据库类型颜色 */
    getDbTypeColor(type) {
      const colors = {
        '0': 'primary',
        '1': 'danger',
        '2': 'warning',
        '3': 'info',
        '4': 'success',
        '5': '',
        '6': 'warning',
        '7': 'danger'
      };
      return colors[type] || '';
    },
    /** 获取数据库类型图标 */
    getDbTypeIcon(type) {
      const icons = {
        '0': 'el-icon-coin',
        '1': 'el-icon-s-data',
        '2': 'el-icon-s-platform',
        '3': 'el-icon-data-board',
        '4': 'el-icon-s-marketing',
        '5': 'el-icon-files',
        '6': 'el-icon-s-cooperation',
        '7': 'el-icon-s-grid'
      };
      return icons[type] || 'el-icon-s-data';
    },
    /** 获取数据库类型图标组件 */
    getDbTypeIconComponent(type) {
      return this.$iconHelper.getDbTypeIconComponent(type);
    },

    /** 工具栏预览按钮 */
    handlePreview() {
      if (this.ids.length !== 1) {
        this.$message.warning('请选择一个数据源进行预览');
        return;
      }
      const dataSource = this.datasourceList.find(item => item.id === this.ids[0]);
      this.previewTables(dataSource);
    },

    /** 工具栏导出按钮 */
    handleDatabaseExport() {
      if (this.ids.length !== 1) {
        this.$message.warning('请选择一个数据源进行导出');
        return;
      }
      const dataSource = this.datasourceList.find(item => item.id === this.ids[0]);
      this.exportDatabase(dataSource);
    },

    /** 预览数据库表 */
    async previewTables(dataSource) {
      this.currentDataSource = dataSource;
      this.previewDialogVisible = true;
      this.previewLoading = true;
      this.previewTableList = [];
      this.selectedPreviewTables = [];

      try {
        const response = await genApi.previewTables({ dataSourceId: dataSource.id });
        this.previewTableList = response.data || [];
      } catch (error) {
        this.$message.error('加载表列表失败：' + error.message);
      } finally {
        this.previewLoading = false;
      }
    },

    /** 导出数据库文档 */
    exportDatabase(dataSource) {
      this.currentDataSource = dataSource;
      this.previewTables(dataSource).then(() => {
        // 预览完成后不显示导出对话框，等用户选择表后再显示
      });
    },

    /** 预览表格选择变化 */
    handlePreviewSelectionChange(selection) {
      this.selectedPreviewTables = selection;
    },

    /** 全选预览表 */
    selectAllPreview() {
      this.$refs.previewTableRef.toggleAllSelection();
    },

    /** 清空预览选择 */
    clearPreviewSelection() {
      this.$refs.previewTableRef.clearSelection();
    },

    /** 显示导出对话框 */
    showExportDialog() {
      if (this.selectedPreviewTables.length === 0) {
        this.$message.warning('请至少选择一张表');
        return;
      }
      this.exportDialogVisible = true;
    },

    /** 确认导出 */
    async handleExportConfirm() {
      this.exporting = true;
      try {
        const tableNames = this.exportConfig.exportScope === 'all'
          ? this.previewTableList.map(t => t.tableName).join(',')
          : this.selectedPreviewTables.map(t => t.tableName).join(',');

        const response = await genApi.exportDatabaseNew({
          tableNames,
          exportType: this.exportConfig.exportType,
          dataSourceId: this.currentDataSource.id
        });

        if (response.code === 200) {
          // 下载文件
          const { filePath, fileName } = response.data;
          this.downloadFile(filePath, fileName);
          this.$message.success('文档导出成功');
          this.exportDialogVisible = false;
        } else {
          this.$message.error(response.msg || '导出失败');
        }
      } catch (error) {
        this.$message.error('导出失败：' + error.message);
      } finally {
        this.exporting = false;
      }
    },

    /** 下载文件 */
    downloadFile(filePath, fileName) {
      const url = `${process.env.VUE_APP_BASE_API}/tool/gen/download?filePath=${encodeURIComponent(filePath)}&fileName=${encodeURIComponent(fileName)}`;
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },

    /** 格式化日期 */
    formatDate(date) {
      if (!date) return '-';
      return new Date(date).toLocaleString('zh-CN');
    }
  }
};
</script>

<style lang="scss" scoped>
.datasource-container {
  padding: 0;
  background: var(--bg-secondary);
  min-height: calc(100vh - 64px);
}

// 页面头部
.page-header {
  position: relative;
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  color: white;
  margin-bottom: var(--spacing-xl);
  overflow: hidden;

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;

    .header-pattern {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
      animation: float 20s ease-in-out infinite;
    }
  }

  .header-content {
    position: relative;
    z-index: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-xl) var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--spacing-lg);
      padding: var(--spacing-lg) var(--spacing-md);
    }
  }

  .header-info {
    flex: 1;

    .page-breadcrumb {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      margin-bottom: var(--spacing-md);
      font-size: 0.9rem;
      opacity: 0.8;

      .breadcrumb-item {
        &.active {
          color: #fff;
          font-weight: 500;
        }
      }

      i {
        font-size: 0.8rem;
      }
    }

    .page-title {
      font-size: 2.5rem;
      font-weight: 800;
      margin: 0 0 var(--spacing-md);
      display: flex;
      align-items: center;
      gap: var(--spacing-md);

      .title-icon {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: var(--border-radius-large);
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);

        i {
          font-size: 28px;
          color: #fff;
        }
      }
    }

    .page-description {
      font-size: 1.1rem;
      line-height: 1.6;
      opacity: 0.9;
      max-width: 600px;
    }
  }

  .header-stats {
    display: flex;
    gap: var(--spacing-md);

    @media (max-width: 768px) {
      width: 100%;
      justify-content: space-between;
    }

    .stat-card {
      background: rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: var(--border-radius-large);
      padding: var(--spacing-lg);
      min-width: 120px;
      text-align: center;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.2);
      }

      &.primary .stat-icon {
        background: linear-gradient(135deg, #4facfe, #00f2fe);
      }

      &.success .stat-icon {
        background: linear-gradient(135deg, #43e97b, #38f9d7);
      }

      &.warning .stat-icon {
        background: linear-gradient(135deg, #fa709a, #fee140);
      }

      .stat-icon {
        width: 40px;
        height: 40px;
        margin: 0 auto var(--spacing-sm);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          font-size: 18px;
          color: white;
        }
      }

      .stat-content {
        .stat-number {
          font-size: 1.8rem;
          font-weight: 700;
          color: white;
          margin-bottom: var(--spacing-xs);
        }

        .stat-label {
          font-size: 0.85rem;
          opacity: 0.8;
        }
      }
    }
  }
}

// 搜索区域
.search-section {
  margin-bottom: var(--spacing-lg);

  .search-card {
    border: none;
    box-shadow: var(--shadow-light);

    .search-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-lg);
      padding-bottom: var(--spacing-md);
      border-bottom: 1px solid var(--border-light);

      .search-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);

        i {
          color: var(--primary-color);
        }
      }

      .close-search {
        color: var(--text-secondary);

        &:hover {
          color: var(--primary-color);
        }
      }
    }

    .search-form {
      .form-row {
        display: flex;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);

        @media (max-width: 768px) {
          flex-direction: column;
          gap: var(--spacing-md);
        }

        .form-item {
          flex: 1;
          margin-bottom: 0;
        }
      }

      .form-actions {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        padding-top: var(--spacing-md);
        border-top: 1px solid var(--border-light);
      }
    }
  }
}

// 工具栏区域
.toolbar-section {
  margin-bottom: var(--spacing-lg);

  .toolbar-card {
    border: none;
    box-shadow: var(--shadow-light);

    .toolbar-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-lg);
      padding-bottom: var(--spacing-md);
      border-bottom: 1px solid var(--border-light);

      .toolbar-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);

        i {
          color: var(--primary-color);
        }
      }

      .selection-info {
        .el-tag {
          background: rgba(79, 172, 254, 0.1);
          border-color: #4facfe;
          color: #4facfe;
        }
      }
    }

    .toolbar-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: var(--spacing-lg);

      @media (max-width: 768px) {
        flex-direction: column;
        gap: var(--spacing-md);
      }

      .primary-actions {
        display: flex;
        gap: var(--spacing-md);

        .primary-btn {
          background: linear-gradient(135deg, #4facfe, #00f2fe);
          border: none;
          padding: var(--spacing-md) var(--spacing-xl);
          font-weight: 600;
          box-shadow: 0 4px 12px rgba(79, 172, 254, 0.3);

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(79, 172, 254, 0.4);
          }
        }

        .secondary-btn {
          padding: var(--spacing-md) var(--spacing-lg);
          font-weight: 500;
        }
      }

      .secondary-actions {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);

        .toolbar-tools {
          display: flex;
          gap: var(--spacing-sm);
        }
      }
    }
  }
}

// 表格区域
.table-section {
  .table-card {
    border: none;
    box-shadow: var(--shadow-light);

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-lg);
      padding-bottom: var(--spacing-md);
      border-bottom: 1px solid var(--border-light);

      .table-title {
        .title-content {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);
          font-size: 1.2rem;
          font-weight: 600;
          color: var(--text-primary);

          i {
            color: var(--primary-color);
            font-size: 1.3rem;
          }

          .total-tag {
            background: rgba(79, 172, 254, 0.1);
            border-color: #4facfe;
            color: #4facfe;
          }
        }
      }
    }

    .datasource-table {
      :deep(.el-table__row) {
        transition: all 0.3s ease;

        &:hover {
          background: rgba(79, 172, 254, 0.03) !important;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
      }

      .table-index {
        font-weight: 600;
        color: var(--primary-color);
        background: rgba(79, 172, 254, 0.1);
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
      }

      .datasource-info {
        .datasource-name {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: var(--spacing-sm);

          .name-wrapper {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);

            .datasource-icon {
              color: var(--primary-color);
              font-size: 1.1rem;
            }

            .name-text {
              font-weight: 600;
              color: var(--text-primary);
              font-size: 1rem;
            }
          }

          .status-tag {
            font-size: 0.75rem;
          }
        }

        .datasource-details {
          .detail-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            color: var(--text-secondary);
            font-size: 0.9rem;
            margin-bottom: var(--spacing-xs);

            i {
              color: var(--primary-color);
              font-size: 0.8rem;
            }
          }
        }
      }

      .db-type {
        .db-tag {
          display: flex;
          align-items: center;
          gap: var(--spacing-xs);
          padding: var(--spacing-xs) var(--spacing-sm);

          i {
            font-size: 0.8rem;
          }
        }
      }

      .auth-info {
        .username, .password {
          display: flex;
          align-items: center;
          gap: var(--spacing-xs);
          color: var(--text-secondary);
          font-size: 0.9rem;
          margin-bottom: var(--spacing-xs);

          i {
            color: var(--primary-color);
            font-size: 0.8rem;
          }
        }
      }

      .table-actions {
        display: flex;
        justify-content: center;

        .action-group {
          display: flex;
          border-radius: var(--border-radius-base);
          overflow: hidden;
          box-shadow: var(--shadow-light);

          .action-btn {
            border-radius: 0;
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;

            &:last-child {
              border-right: none;
            }

            &:hover {
              transform: translateY(-1px);
              z-index: 1;
            }

            &.test-btn {
              background: #4facfe;
              border-color: #4facfe;
              color: white;

              &:hover {
                background: #6fc3ff;
              }
            }

            &.edit-btn {
              background: #67c23a;
              border-color: #67c23a;
              color: white;

              &:hover {
                background: #85ce61;
              }
            }

            &.switch-btn {
              background: #e6a23c;
              border-color: #e6a23c;
              color: white;

              &:hover {
                background: #ebb563;
              }
            }

            &.preview-btn {
              background: #909399;
              border-color: #909399;
              color: white;

              &:hover {
                background: #a6a9ad;
              }
            }

            &.export-btn {
              background: #67c23a;
              border-color: #67c23a;
              color: white;

              &:hover {
                background: #85ce61;
              }
            }

            &.delete-btn {
              background: #f56c6c;
              border-color: #f56c6c;
              color: white;

              &:hover {
                background: #f78989;
              }
            }
          }
        }
      }
    }

    .pagination-wrapper {
      margin-top: var(--spacing-lg);
      display: flex;
      justify-content: center;
    }
  }
}

// 动画效果
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

// 预览对话框样式
.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  background: rgba(79, 172, 254, 0.05);
  border-radius: var(--border-radius-base);
  border: 1px solid rgba(79, 172, 254, 0.1);

  .preview-info {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;

    .el-tag {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);

      i {
        font-size: 0.9rem;
      }
    }
  }

  .preview-actions {
    display: flex;
    gap: var(--spacing-sm);
  }
}

.table-name {
  font-weight: 600;
  color: var(--primary-color);
}

.table-comment {
  color: var(--text-secondary);
}

// 导出配置样式
.export-config {
  .el-form-item {
    margin-bottom: var(--spacing-lg);
  }

  .el-radio-group {
    .el-radio-button {
      margin-right: var(--spacing-sm);

      .el-radio-button__inner {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        padding: var(--spacing-sm) var(--spacing-md);

        i {
          font-size: 1rem;
        }
      }
    }
  }

  .el-tag {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
  }
}

// 响应式适配
@media (max-width: 768px) {
  .datasource-container {
    padding: 0 var(--spacing-sm);
  }

  .table-card {
    .datasource-table {
      .table-actions {
        .action-group {
          flex-direction: column;

          .action-btn {
            border-right: none;
            border-bottom: 1px solid rgba(255, 255, 255, 0.2);

            &:last-child {
              border-bottom: none;
            }
          }
        }
      }
    }
  }

  .preview-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;

    .preview-info {
      justify-content: center;
    }

    .preview-actions {
      justify-content: center;
    }
  }

  .export-config {
    .el-radio-group {
      .el-radio-button {
        display: block;
        margin-bottom: var(--spacing-sm);
        margin-right: 0;
      }
    }
  }
}
</style>
