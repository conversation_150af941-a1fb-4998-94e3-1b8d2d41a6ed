<template>
  <div class="database-export-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-background">
        <div class="header-pattern"></div>
      </div>
      <div class="header-content">
        <div class="header-info">
          <div class="page-breadcrumb">
            <span class="breadcrumb-item">工作台</span>
            <el-icon><ArrowRight /></el-icon>
            <span class="breadcrumb-item active">数据库文档导出</span>
          </div>
          <h1 class="page-title">
            <div class="title-icon">
              <el-icon><Document /></el-icon>
            </div>
            <span>数据库文档导出</span>
          </h1>
          <p class="page-description">选择数据源，预览表结构，导出数据库设计文档，支持多种格式</p>
        </div>
        <div class="header-stats">
          <div class="stat-card primary">
            <div class="stat-icon">
              <el-icon><Grid /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ dataSourceList.length }}</div>
              <div class="stat-label">数据源总数</div>
            </div>
          </div>
          <div class="stat-card success">
            <div class="stat-icon">
              <el-icon><View /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ previewTableList.length }}</div>
              <div class="stat-label">预览表数</div>
            </div>
          </div>
          <div class="stat-card warning">
            <div class="stat-icon">
              <el-icon><Download /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ exportCount || 0 }}</div>
              <div class="stat-label">已导出</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section" v-show="showSearch">
      <el-card class="search-card">
        <div class="search-header">
          <div class="search-title">
            <el-icon><Search /></el-icon>
            <span>筛选条件</span>
          </div>
          <el-button type="text" @click="showSearch = false" class="close-search">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
        <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
          <div class="form-row">
            <el-form-item label="数据源名称" prop="name" class="form-item">
              <el-input
                v-model="queryParams.name"
                placeholder="请输入数据源名称进行搜索"
                clearable
                :prefix-icon="Grid"
                @keyup.enter.native="handleQuery"
              />
            </el-form-item>
            <el-form-item label="数据库类型" class="form-item">
              <el-select v-model="queryParams.type" placeholder="请选择数据库类型" clearable>
                <el-option label="MySQL" value="0" />
                <el-option label="PostgreSQL" value="1" />
                <el-option label="Oracle" value="2" />
                <el-option label="SQL Server" value="3" />
                <el-option label="SQLite" value="4" />
                <el-option label="MongoDB" value="5" />
                <el-option label="Redis" value="6" />
                <el-option label="其他" value="7" />
              </el-select>
            </el-form-item>
          </div>
          <div class="form-actions">
            <el-button type="primary" @click="handleQuery">
          <template #icon><el-icon><Search /></el-icon></template>
              搜索数据源
            </el-button>
            <el-button @click="resetQuery">
          <template #icon><el-icon><Refresh /></el-icon></template>
              重置条件
            </el-button>
          </div>
        </el-form>
      </el-card>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar-section">
      <el-card class="toolbar-card">
        <div class="toolbar-header">
          <div class="toolbar-title">
            <el-icon><Setting /></el-icon>
            <span>数据源操作</span>
          </div>
          <div class="selection-info" v-if="selectedDataSource">
            <el-tag type="primary" size="small">
              当前数据源：{{ selectedDataSource.name }}
            </el-tag>
          </div>
        </div>

        <div class="toolbar-content">
          <div class="export-options">
            <div class="export-group">
              <h4 class="group-title">
                <el-icon><View /></el-icon>
                预览操作
              </h4>
              <div class="export-buttons">
                <el-button
                  type="info"
                  @click="handlePreviewSelected"
                  :disabled="!selectedDataSource"
                  size="medium"
                  class="export-btn preview-btn"
                >
                  <template #icon><el-icon><View /></el-icon></template>
                  预览表结构
                </el-button>
                <el-button
                  type="success"
                  @click="selectAllTables"
                  :disabled="previewTableList.length === 0"
                  size="medium"
                  class="export-btn select-btn"
                >
                  <template #icon><el-icon><Check /></el-icon></template>
                  全选表格
                </el-button>
                <el-button
                  type="warning"
                  @click="clearTableSelection"
                  :disabled="selectedTables.length === 0"
                  size="medium"
                  class="export-btn clear-btn"
                >
                  <template #icon><el-icon><Close /></el-icon></template>
                  清空选择
                </el-button>
              </div>
            </div>

            <div class="export-group">
              <h4 class="group-title">
                <el-icon><Document /></el-icon>
                文档导出
              </h4>
              <div class="export-buttons">
                <el-button
                  type="primary"
                  @click="showExportDialog"
                  :disabled="selectedTables.length === 0"
                  size="medium"
                  class="export-btn export-btn-main"
                >
                  <template #icon><el-icon><Download /></el-icon></template>
                  导出选中表
                </el-button>
                <el-button
                  type="success"
                  @click="exportAllTables"
                  :disabled="!selectedDataSource"
                  size="medium"
                  class="export-btn export-all-btn"
                >
                  <template #icon><el-icon><Grid /></el-icon></template>
                  导出所有表
                </el-button>
              </div>
            </div>
          </div>

          <div class="toolbar-tools">
            <el-tooltip content="筛选搜索" placement="top">
              <el-button
                circle
                @click="showSearch = !showSearch"
                :type="showSearch ? 'primary' : ''"
                size="medium"
              >
                <template #icon><el-icon><Search /></el-icon></template>
              </el-button>
            </el-tooltip>
            <el-tooltip content="刷新数据" placement="top">
              <el-button circle @click="loadDataSources" size="medium">
          <template #icon><el-icon><Refresh /></el-icon></template>
        </el-button>
            </el-tooltip>
            <el-tooltip content="导出设置" placement="top">
              <el-button circle size="medium">
          <template #icon><el-icon><Setting /></el-icon></template>
        </el-button>
            </el-tooltip>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 数据源表格 -->
    <div class="table-section">
      <el-card class="table-card">
        <div class="table-header">
          <div class="table-title">
            <div class="title-content">
              <el-icon><Grid /></el-icon>
              <span>数据源列表</span>
              <el-tag v-if="dataSourceList.length > 0" type="info" size="mini" class="total-tag">
                共 {{ dataSourceList.length }} 个数据源
              </el-tag>
            </div>
          </div>
        </div>

        <el-table
          v-loading="loading"
          :data="dataSourceList"
          @current-change="handleCurrentChange"
          highlight-current-row
          class="datasource-table"
          :empty-text="loading ? '加载中...' : '暂无数据源'"
        >
          <el-table-column label="序号" width="60" align="center">
            <template #default="scope">
              <span class="table-index">{{ scope.$index + 1 }}</span>
            </template>
          </el-table-column>

          <el-table-column label="数据源信息" min-width="300">
            <template #default="scope">
              <div class="datasource-info">
                <div class="datasource-name">
                  <div class="name-wrapper">
                    <el-icon class="datasource-icon"><component :is="getDbTypeIconComponent(scope.row.type)" /></el-icon>
                    <span class="name-text">{{ scope.row.name }}</span>
                  </div>
                </div>
                <div class="datasource-detail">
                  <span class="detail-item">
                    <el-icon><Platform /></el-icon>
                    {{ scope.row.dbName }}
                  </span>
                  <span class="detail-item">
                    <el-icon><Position /></el-icon>
                    {{ scope.row.url }}:{{ scope.row.port }}
                  </span>
                </div>
                <div class="datasource-meta">
                  <el-tag size="mini" type="info" class="type-tag">
                    {{ getDbTypeName(scope.row.type) }}
                  </el-tag>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="连接信息" width="260" align="center">
            <template #default="scope">
              <div class="connection-info">
                <div class="connection-item">
                  <span class="connection-label">用户名:</span>
                  <span class="connection-value">{{ scope.row.username }}</span>
                </div>
                <div class="connection-item">
                  <span class="connection-label">创建时间:</span>
                  <span class="connection-value">{{ formatDate(scope.row.createTime) }}</span>
                </div>
              </div>
            </template>
          </el-table-column>

          <el-table-column label="操作" width="280" align="center" fixed="right">
            <template #default="scope">
              <div class="table-actions">
                <el-button-group class="action-group">
                  <el-tooltip content="测试连接" placement="top">
                    <el-button
                      type="primary"
                      size="mini"
                      @click="testConnection(scope.row)"
                      class="action-btn test-btn"
                    >
                      <template #icon><el-icon><Link /></el-icon></template>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="预览表结构" placement="top">
                    <el-button
                      type="info"
                      size="mini"
                      @click="previewTables(scope.row)"
                      class="action-btn preview-btn"
                    >
                      <template #icon><el-icon><View /></el-icon></template>
                    </el-button>
                  </el-tooltip>
                  <el-tooltip content="导出文档" placement="top">
                    <el-button
                      type="success"
                      size="mini"
                      @click="exportDatabase(scope.row)"
                      class="action-btn export-btn"
                    >
                      <template #icon><el-icon><download /></el-icon></template>
                    </el-button>
                  </el-tooltip>
                </el-button-group>
              </div>
            </template>
          </el-table-column>
        </el-table>
      </el-card>
    </div>

    <!-- 表预览对话框 -->
    <el-dialog
      title="数据库表预览"
      :visible.sync="previewDialogVisible"
      width="1200px"
      append-to-body
      :close-on-click-modal="false"
    >
      <div class="preview-header">
        <div class="preview-info">
          <el-tag type="primary" size="medium">
            <el-icon><Grid /></el-icon>
            {{ currentDataSource.name }}
          </el-tag>
          <el-tag type="info" size="medium">
            共 {{ previewTableList.length }} 张表
          </el-tag>
          <el-tag type="success" size="medium" v-if="selectedTables.length > 0">
            已选择 {{ selectedTables.length }} 张表
          </el-tag>
        </div>
        <div class="preview-actions">
          <el-button
            type="success"
            :icon="Check"
            size="small"
            @click="selectAllTables"
          >
            全选
          </el-button>
          <el-button
            type="warning"
            :icon="Close"
            size="small"
            @click="clearTableSelection"
          >
            清空
          </el-button>
          <el-button
            type="primary"
            :icon="Download"
            size="small"
            @click="showExportDialog"
            :disabled="selectedTables.length === 0"
          >
            导出选中
          </el-button>
        </div>
      </div>

      <el-table
        ref="previewTableRef"
        :data="previewTableList"
        v-loading="previewLoading"
        @selection-change="handleTableSelectionChange"
        style="width: 100%; margin-top: 15px;"
        max-height="400"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column prop="tableName" label="表名" min-width="200">
          <template #default="scope">
            <span class="table-name">{{ scope.row.tableName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="tableComment" label="表注释" min-width="250">
          <template #default="scope">
            <span class="table-comment">{{ scope.row.tableComment || '暂无注释' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" label="创建时间" width="180">
          <template #default="scope">
            <span>{{ formatDate(scope.row.createTime) }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="updateTime" label="更新时间" width="180">
          <template #default="scope">
            <span>{{ formatDate(scope.row.updateTime) }}</span>
          </template>
        </el-table-column>
      </el-table>

      <div slot="footer" class="dialog-footer">
        <el-button @click="previewDialogVisible = false">关 闭</el-button>
        <el-button
          type="primary"
          @click="showExportDialog"
          :disabled="selectedTables.length === 0"
        >
          导出选中表
        </el-button>
      </div>
    </el-dialog>

    <!-- 导出配置对话框 -->
    <el-dialog
      title="数据库文档导出"
      :visible.sync="exportDialogVisible"
      width="688px"
      append-to-body
      :close-on-click-modal="false"
    >
      <div class="export-config">
        <el-form :model="exportConfig" label-width="120px">
          <el-form-item label="数据源：">
            <el-tag type="primary">{{ currentDataSource.name }}</el-tag>
          </el-form-item>

          <el-form-item label="导出范围：">
            <el-radio-group v-model="exportConfig.exportScope">
              <el-radio label="selected">仅选中的表 ({{ selectedTables.length }} 张)</el-radio>
              <el-radio label="all">所有表 ({{ previewTableList.length }} 张)</el-radio>
            </el-radio-group>
          </el-form-item>

          <el-form-item label="导出格式：">
            <el-radio-group v-model="exportConfig.exportType">
              <el-radio-button label="WORD">
                <el-icon><Document /></el-icon>
                Word文档
              </el-radio-button>
              <el-radio-button label="PDF">
                <el-icon><DocumentCopy /></el-icon>
                PDF文档
              </el-radio-button>
              <el-radio-button label="MARKDOWN">
                <el-icon><DocumentCopy /></el-icon>
                MarkDown文档
              </el-radio-button>
              <el-radio-button label="HTML">
                <el-icon><Monitor /></el-icon>
                HTML文档
              </el-radio-button>
            </el-radio-group>
          </el-form-item>
        </el-form>
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="exportDialogVisible = false">取 消</el-button>
        <el-button
          type="primary"
          @click="handleExportConfirm"
          :loading="exporting"
        >
          {{ exporting ? '导出中...' : '开始导出' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import genApi from '@/api/gen'
import dataSourceApi from '@/api/dataSource'
import IconHelper from '@/utils/icon-helper'
import {
  ArrowRight,
  Document,
  Grid,
  View,
  Download,
  Search,
  Close,
  Refresh,
  Setting,
  Check,
  Platform,
  Position,
  Link,
  DocumentCopy,
  Monitor,
  Coin,
  Files,
  TrendCharts,
  Connection
} from '@element-plus/icons-vue';

export default {
  name: "DatabaseExport",
  setup() {
    return {
      $iconHelper: IconHelper
    }
  },
  components: {
    ArrowRight,
    Document,
    Grid,
    View,
    Download,
    Search,
    Close,
    Refresh,
    Setting,
    Check,
    Platform,
    Position,
    Link,
    DocumentCopy,
    Monitor,
    Coin,
    Files,
    TrendCharts,
    Connection
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 显示搜索条件
      showSearch: false,
      // 数据源列表
      dataSourceList: [],
      // 当前选中的数据源
      selectedDataSource: null,
      // 查询参数
      queryParams: {
        name: undefined,
        type: undefined
      },

      // 预览相关
      previewDialogVisible: false,
      previewLoading: false,
      previewTableList: [],
      selectedTables: [],
      currentDataSource: {},

      // 导出相关
      exportDialogVisible: false,
      exporting: false,
      exportConfig: {
        exportType: 'WORD',
        exportScope: 'selected'
      },

      // 导出统计
      exportCount: 0,
    };
  },
  created() {
    this.loadDataSources();
  },
  methods: {
    /** 加载数据源列表 */
    async loadDataSources() {
      this.loading = true;
      try {
        const response = await dataSourceApi.listDatasource();
        this.dataSourceList = response.rows || [];
      } catch (error) {
        this.$message.error('加载数据源列表失败');
      } finally {
        this.loading = false;
      }
    },

    /** 搜索按钮操作 */
    handleQuery() {
      // 根据查询条件过滤数据源
      this.loadDataSources();
    },
    /** 重置查询 */
    resetQuery() {
      this.queryParams = {
        name: undefined,
        type: undefined
      };
      this.handleQuery();
    },

    /** 当前行变化 */
    handleCurrentChange(currentRow) {
      this.selectedDataSource = currentRow;
    },

    /** 测试数据源连接 */
    async testConnection(dataSource) {
      try {
        const response = await dataSourceApi.testConnection(dataSource);
        if (response.code === 200) {
          this.$message.success('数据源连接测试成功');
        } else {
          this.$message.error('数据源连接测试失败：' + response.msg);
        }
      } catch (error) {
        this.$message.error('数据源连接测试失败：' + error.message);
      }
    },
    /** 预览数据库表 */
    async previewTables(dataSource) {
      this.currentDataSource = dataSource;
      this.selectedDataSource = dataSource;
      this.previewDialogVisible = true;
      this.previewLoading = true;
      this.previewTableList = [];
      this.selectedTables = [];

      try {
        const response = await genApi.previewTables({ dataSourceId: dataSource.id });
        this.previewTableList = response.data || [];
      } catch (error) {
        this.$message.error('加载表列表失败：' + error.message);
      } finally {
        this.previewLoading = false;
      }
    },

    /** 工具栏预览按钮 */
    handlePreviewSelected() {
      if (!this.selectedDataSource) {
        this.$message.warning('请先选择一个数据源');
        return;
      }
      this.previewTables(this.selectedDataSource);
    },
    /** 导出数据库文档 */
    exportDatabase(dataSource) {
      this.currentDataSource = dataSource;
      this.selectedDataSource = dataSource;
      this.previewTables(dataSource);
    },

    /** 表格选择变化 */
    handleTableSelectionChange(selection) {
      this.selectedTables = selection;
    },

    /** 全选表格 */
    selectAllTables() {
      this.$refs.previewTableRef.toggleAllSelection();
    },

    /** 清空表格选择 */
    clearTableSelection() {
      this.$refs.previewTableRef.clearSelection();
    },

    /** 显示导出对话框 */
    showExportDialog() {
      if (this.selectedTables.length === 0) {
        this.$message.warning('请至少选择一张表');
        return;
      }
      this.exportDialogVisible = true;
    },

    /** 导出所有表 */
    exportAllTables() {
      if (!this.selectedDataSource) {
        this.$message.warning('请先选择一个数据源');
        return;
      }
      this.exportConfig.exportScope = 'all';
      this.previewTables(this.selectedDataSource).then(() => {
        this.exportDialogVisible = true;
      });
    },
    /** 确认导出 */
    async handleExportConfirm() {
      this.exporting = true;
      try {
        const tableNames = this.exportConfig.exportScope === 'all'
          ? this.previewTableList.map(t => t.tableName).join(',')
          : this.selectedTables.map(t => t.tableName).join(',');

        const response = await genApi.exportDatabaseNew({
          tableNames,
          exportType: this.exportConfig.exportType,
          dataSourceId: this.currentDataSource.id
        });

        if (response.code === 200) {
          // 下载文件
          const { filePath, fileName } = response.data;
          this.downloadFile(filePath, fileName);
          this.$message.success('文档导出成功');
          this.exportDialogVisible = false;
          this.exportCount++;
        } else {
          this.$message.error(response.msg || '导出失败');
        }
      } catch (error) {
        this.$message.error('导出失败：' + error.message);
      } finally {
        this.exporting = false;
      }
    },

    /** 下载文件 */
    downloadFile(filePath, fileName) {
      const url = `${process.env.VUE_APP_BASE_API}/tool/gen/download?filePath=${encodeURIComponent(filePath)}&fileName=${encodeURIComponent(fileName)}`;
      const link = document.createElement('a');
      link.href = url;
      link.download = fileName;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    },
    /** 获取数据库类型图标 */
    getDbTypeIcon(type) {
      const icons = {
        '0': 'el-icon-coin',
        '1': 'el-icon-s-data',
        '2': 'el-icon-s-platform',
        '3': 'el-icon-data-board',
        '4': 'el-icon-s-marketing',
        '5': 'el-icon-files',
        '6': 'el-icon-s-cooperation',
        '7': 'el-icon-s-grid'
      }
      return icons[type] || 'el-icon-s-data';
    },
    /** 获取数据库类型图标组件 */
    getDbTypeIconComponent(type) {
      return this.$iconHelper.getDbTypeIconComponent(type);
    },

    /** 获取数据库类型名称 */
    getDbTypeName(type) {
      const names = {
        '0': 'MySQL',
        '1': 'PostgreSQL',
        '2': 'Oracle',
        '3': 'SQL Server',
        '4': 'SQLite',
        '5': 'MongoDB',
        '6': 'Redis',
        '7': '其他'
      };
      return names[type] || '未知';
    },

    /** 格式化日期 */
    formatDate(date) {
      if (!date) return '-';
      return new Date(date).toLocaleString('zh-CN');
    }
  }
};
</script>

<style lang="scss" scoped>
.database-export-container {
  padding: 0;
  background: var(--bg-secondary);
  min-height: calc(100vh - 64px);
}

// 页面头部
.page-header {
  position: relative;
  background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
  color: white;
  margin-bottom: var(--spacing-xl);
  overflow: hidden;

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;

    .header-pattern {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
      animation: float 20s ease-in-out infinite;
    }
  }

  .header-content {
    position: relative;
    z-index: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-xl) var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--spacing-lg);
      padding: var(--spacing-lg) var(--spacing-md);
    }
  }

  .header-info {
    flex: 1;

    .page-breadcrumb {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      margin-bottom: var(--spacing-md);
      font-size: 0.9rem;
      opacity: 0.8;

      .breadcrumb-item {
        &.active {
          color: #fff;
          font-weight: 500;
        }
      }

      i {
        font-size: 0.8rem;
      }
    }

    .page-title {
      font-size: 2.5rem;
      font-weight: 800;
      margin: 0 0 var(--spacing-md);
      display: flex;
      align-items: center;
      gap: var(--spacing-md);

      .title-icon {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: var(--border-radius-large);
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);

        i {
          font-size: 28px;
          color: #fff;
        }
      }
    }

    .page-description {
      font-size: 1.1rem;
      line-height: 1.6;
      opacity: 0.9;
      max-width: 600px;
    }
  }

  .header-stats {
    display: flex;
    gap: var(--spacing-md);

    @media (max-width: 768px) {
      width: 100%;
      justify-content: space-between;
    }

    .stat-card {
      background: rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: var(--border-radius-large);
      padding: var(--spacing-lg);
      min-width: 120px;
      text-align: center;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.2);
      }

      &.primary .stat-icon {
        background: linear-gradient(135deg, #fa709a, #fee140);
      }

      &.success .stat-icon {
        background: linear-gradient(135deg, #43e97b, #38f9d7);
      }

      &.warning .stat-icon {
        background: linear-gradient(135deg, #fa709a, #fee140);
      }

      .stat-icon {
        width: 40px;
        height: 40px;
        margin: 0 auto var(--spacing-sm);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          font-size: 18px;
          color: white;
        }
      }

      .stat-content {
        .stat-number {
          font-size: 1.8rem;
          font-weight: 700;
          color: white;
          margin-bottom: var(--spacing-xs);
        }

        .stat-label {
          font-size: 0.85rem;
          opacity: 0.8;
        }
      }
    }
  }
}

// 搜索区域
.search-section {
  margin-bottom: var(--spacing-lg);

  .search-card {
    border: none;
    box-shadow: var(--shadow-light);

    .search-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-lg);
      padding-bottom: var(--spacing-md);
      border-bottom: 1px solid var(--border-light);

      .search-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);

        i {
          color: var(--primary-color);
        }
      }

      .close-search {
        color: var(--text-secondary);

        &:hover {
          color: var(--primary-color);
        }
      }
    }

    .search-form {
      .form-row {
        display: flex;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);

        @media (max-width: 768px) {
          flex-direction: column;
          gap: var(--spacing-md);
        }

        .form-item {
          flex: 1;
          margin-bottom: 0;
        }
      }

      .form-actions {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        padding-top: var(--spacing-md);
        border-top: 1px solid var(--border-light);
      }
    }
  }
}

// 工具栏区域
.toolbar-section {
  margin-bottom: var(--spacing-lg);

  .toolbar-card {
    border: none;
    box-shadow: var(--shadow-light);

    .toolbar-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-lg);
      padding-bottom: var(--spacing-md);
      border-bottom: 1px solid var(--border-light);

      .toolbar-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);

        i {
          color: var(--primary-color);
        }
      }

      .selection-info {
        .el-tag {
          background: rgba(250, 112, 154, 0.1);
          border-color: #fa709a;
          color: #fa709a;
        }
      }
    }

    .toolbar-content {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      gap: var(--spacing-xl);

      @media (max-width: 768px) {
        flex-direction: column;
        gap: var(--spacing-lg);
      }

      .export-options {
        flex: 1;
        display: flex;
        gap: var(--spacing-xl);

        @media (max-width: 768px) {
          flex-direction: column;
          gap: var(--spacing-lg);
        }

        .export-group {
          flex: 1;

          .group-title {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: 1rem;
            font-weight: 600;
            color: var(--text-primary);
            margin-bottom: var(--spacing-md);

            i {
              color: var(--primary-color);
            }
          }

          .export-buttons {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-sm);

            .export-btn {
              justify-content: flex-start;
              padding: var(--spacing-md) var(--spacing-lg);
              font-weight: 500;
              transition: all 0.3s ease;

              &:hover {
                transform: translateY(-2px);
              }

              &.excel-btn {
                background: linear-gradient(135deg, #67c23a, #85ce61);
                border: none;
                color: white;

                &:hover {
                  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
                }
              }

              &.word-btn {
                background: linear-gradient(135deg, #409eff, #66b1ff);
                border: none;
                color: white;

                &:hover {
                  box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
                }
              }

              &.pdf-btn {
                background: linear-gradient(135deg, #f56c6c, #f78989);
                border: none;
                color: white;

                &:hover {
                  box-shadow: 0 4px 12px rgba(245, 108, 108, 0.3);
                }
              }

              &.sync-btn {
                background: linear-gradient(135deg, #e6a23c, #ebb563);
                border: none;
                color: white;

                &:hover {
                  box-shadow: 0 4px 12px rgba(230, 162, 60, 0.3);
                }
              }

              &.import-btn {
                background: linear-gradient(135deg, #909399, #b1b3b8);
                border: none;
                color: white;

                &:hover {
                  box-shadow: 0 4px 12px rgba(144, 147, 153, 0.3);
                }
              }
            }
          }
        }
      }

      .toolbar-tools {
        display: flex;
        gap: var(--spacing-sm);
        align-items: flex-start;
      }
    }
  }
}

// 表格区域
.table-section {
  .table-card {
    border: none;
    box-shadow: var(--shadow-light);

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-lg);
      padding-bottom: var(--spacing-md);
      border-bottom: 1px solid var(--border-light);

      .table-title {
        .title-content {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);
          font-size: 1.2rem;
          font-weight: 600;
          color: var(--text-primary);

          i {
            color: var(--primary-color);
            font-size: 1.3rem;
          }

          .total-tag {
            background: rgba(250, 112, 154, 0.1);
            border-color: #fa709a;
            color: #fa709a;
          }
        }
      }

      .table-actions {
        .view-toggle {
          .el-button {
            padding: var(--spacing-xs) var(--spacing-sm);
            font-size: 0.9rem;
          }
        }
      }
    }

    .datasource-table {
      :deep(.el-table__row) {
        transition: all 0.3s ease;

        &:hover {
          background: rgba(250, 112, 154, 0.03) !important;
          transform: translateY(-1px);
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
      }

      .table-index {
        font-weight: 600;
        color: var(--primary-color);
        background: rgba(250, 112, 154, 0.1);
        border-radius: 50%;
        width: 24px;
        height: 24px;
        display: inline-flex;
        align-items: center;
        justify-content: center;
        font-size: 12px;
      }

      .datasource-info {
        .datasource-name {
          display: flex;
          justify-content: space-between;
          align-items: center;
          margin-bottom: var(--spacing-sm);

          .name-wrapper {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);

            .datasource-icon {
              color: var(--primary-color);
              font-size: 1.1rem;
            }

            .name-text {
              font-weight: 600;
              color: var(--text-primary);
              font-size: 1rem;
            }
          }

          .status-tag {
            font-size: 0.75rem;
          }
        }

        .datasource-detail {
          display: flex;
          flex-direction: column;
          gap: var(--spacing-xs);
          margin-bottom: var(--spacing-sm);

          .detail-item {
            display: flex;
            align-items: center;
            gap: var(--spacing-xs);
            color: var(--text-secondary);
            font-size: 0.9rem;

            i {
              color: var(--primary-color);
              font-size: 0.8rem;
            }
          }
        }

        .datasource-meta {
          .type-tag {
            background: rgba(103, 194, 58, 0.1);
            border-color: #67c23a;
            color: #67c23a;
          }
        }
      }

      .connection-info {
        .connection-item {
          display: flex;
          align-items: center;
          gap: var(--spacing-xs);
          margin-bottom: var(--spacing-xs);
          font-size: 0.9rem;

          .connection-label {
            color: var(--text-secondary);
            min-width: 60px;
          }

          .connection-value {
            color: var(--text-primary);
          }
        }
      }



      .table-actions {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        justify-content: center;

        .action-group {
          display: flex;
          border-radius: var(--border-radius-base);
          overflow: hidden;
          box-shadow: var(--shadow-light);

          .action-btn {
            border-radius: 0;
            border-right: 1px solid rgba(255, 255, 255, 0.2);
            transition: all 0.3s ease;

            &:last-child {
              border-right: none;
            }

            &:hover {
              transform: translateY(-1px);
              z-index: 1;
            }

            &.test-btn {
              background: #409eff;
              border-color: #409eff;
              color: white;

              &:hover {
                background: #66b1ff;
              }
            }

            &.preview-btn {
              background: #909399;
              border-color: #909399;
              color: white;

              &:hover {
                background: #a6a9ad;
              }
            }

            &.export-btn {
              background: #67c23a;
              border-color: #67c23a;
              color: white;

              &:hover {
                background: #85ce61;
              }
            }
          }
        }

        .export-dropdown {
          .export-main-btn {
            background: linear-gradient(135deg, #fa709a, #fee140);
            border: none;
            color: white;
            padding: var(--spacing-xs) var(--spacing-md);
            border-radius: var(--border-radius-base);
            font-weight: 500;
            box-shadow: 0 2px 8px rgba(250, 112, 154, 0.3);
            transition: all 0.3s ease;

            &:hover {
              transform: translateY(-2px);
              box-shadow: 0 4px 12px rgba(250, 112, 154, 0.4);
            }
          }
        }
      }
    }

    .pagination-wrapper {
      margin-top: var(--spacing-lg);
      display: flex;
      justify-content: center;
    }
  }
}

// 预览对话框样式
.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-md);
  padding: var(--spacing-md);
  background: rgba(79, 172, 254, 0.05);
  border-radius: var(--border-radius-base);
  border: 1px solid rgba(79, 172, 254, 0.1);

  .preview-info {
    display: flex;
    gap: var(--spacing-sm);
    align-items: center;

    .el-tag {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);

      i {
        font-size: 0.9rem;
      }
    }
  }

  .preview-actions {
    display: flex;
    gap: var(--spacing-sm);
  }
}

.table-name {
  font-weight: 600;
  color: var(--primary-color);
}

.table-comment {
  color: var(--text-secondary);
}

// 导出配置样式
.export-config {
  .el-form-item {
    margin-bottom: var(--spacing-lg);
  }
  //
  //.el-radio-group {
  //  .el-radio-button {
  //    margin-right: var(--spacing-sm);
  //
  //    .el-radio-button__inner {
  //      //display: flex;
  //      align-items: center;
  //      //gap: var(--spacing-xs);
  //      //padding: var(--spacing-sm) var(--spacing-md);
  //
  //      i {
  //        font-size: 1rem;
  //      }
  //    }
  //  }
  //}

  .el-tag {
    display: flex;
    align-items: center;
    gap: var(--spacing-xs);
  }
}

// 动画效果
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

// 响应式适配
@media (max-width: 768px) {
  .database-export-container {
    padding: 0 var(--spacing-sm);
  }

  .table-card {
    .datasource-table {
      .table-actions {
        flex-direction: column;
        gap: var(--spacing-xs);

        .action-group {
          width: 100%;
        }
      }
    }
  }

  .preview-header {
    flex-direction: column;
    gap: var(--spacing-md);
    align-items: stretch;

    .preview-info {
      justify-content: center;
    }

    .preview-actions {
      justify-content: center;
    }
  }

  .export-config {
    .el-radio-group {
      .el-radio-button {
        display: block;
        margin-bottom: var(--spacing-sm);
        margin-right: 0;
      }
    }
  }
}
</style>

