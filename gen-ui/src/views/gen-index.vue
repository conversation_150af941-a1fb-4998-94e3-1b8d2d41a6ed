<template>
  <div class="gen-container">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-background">
        <div class="header-pattern"></div>
      </div>
      <div class="header-content">
        <div class="header-info">
          <div class="page-breadcrumb">
            <span class="breadcrumb-item">工作台</span>
            <el-icon><ArrowRight /></el-icon>
            <span class="breadcrumb-item active">代码生成</span>
          </div>
          <h1 class="page-title">
            <div class="title-icon">
              <el-icon><MagicStick /></el-icon>
            </div>
            <span>智能代码生成</span>
          </h1>
          <p class="page-description">基于数据表结构，智能生成高质量的前后端代码，支持多种技术栈组合</p>
        </div>
        <div class="header-stats">
          <div class="stat-card primary">
            <div class="stat-icon">
              <el-icon><Grid /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ total }}</div>
              <div class="stat-label">数据表总数</div>
            </div>
          </div>
          <div class="stat-card success">
            <div class="stat-icon">
              <el-icon><Check /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ ids.length }}</div>
              <div class="stat-label">已选择表</div>
            </div>
          </div>
          <div class="stat-card info">
            <div class="stat-icon">
              <el-icon><Download /></el-icon>
            </div>
            <div class="stat-content">
              <div class="stat-number">{{ generatedCount || 0 }}</div>
              <div class="stat-label">已生成</div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 搜索区域 -->
    <div class="search-section" v-show="showSearch">
      <el-card class="search-card">
        <div class="search-header">
          <div class="search-title">
            <el-icon><Search /></el-icon>
            <span>筛选条件</span>
          </div>
          <el-button type="text" @click="showSearch = false" class="close-search">
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
        <el-form :model="queryParams" ref="queryForm" :inline="true" class="search-form">
          <div class="form-row">
            <el-form-item label="表名称" prop="tableName" class="form-item">
              <el-input
                v-model="queryParams.tableName"
                placeholder="请输入表名称进行搜索"
                clearable
                :prefix-icon="Grid"
                @keyup.enter.native="handleQuery"
                class="search-input"
              />
            </el-form-item>
            <el-form-item label="创建时间" class="form-item">
              <el-date-picker
                v-model="dateRange"
                style="width: 300px"
                value-format="yyyy-MM-dd"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期"
                class="date-picker"
                :prefix-icon="Calendar"
              />
            </el-form-item>
            <el-button type="primary" @click="handleQuery" class="action-btn">
          <template #icon><el-icon><Search /></el-icon></template>
              搜索表格
            </el-button>
            <el-button @click="resetQuery" class="action-btn">
          <template #icon><el-icon><Refresh /></el-icon></template>
              重置条件
            </el-button>
          </div>
        </el-form>
      </el-card>
    </div>

    <!-- 操作工具栏 -->
    <div class="toolbar-section">
      <el-card class="toolbar-card">
        <div class="toolbar-header">
          <div class="toolbar-title">
            <el-icon><Setting /></el-icon>
            <span>操作面板</span>
          </div>
          <div class="selection-info" v-if="ids.length > 0">
            <el-tag type="primary" size="small">已选择 {{ ids.length }} 个表</el-tag>
          </div>
        </div>

        <div class="toolbar-content">
          <div class="primary-actions">
            <div class="datasource-selector">
              <label class="datasource-label">数据源:</label>
              <el-select
                v-model="selectedDataSourceId"
                placeholder="选择数据源"
                @change="handleDataSourceChange"
                size="medium"
                class="datasource-select"
                clearable
              >
                <el-option
                  v-for="ds in dataSourceList"
                  :key="ds.id"
                  :label="ds.name"
                  :value="ds.id"
                >
                  <span style="float: left">{{ ds.name }}</span>
                  <span style="float: right; color: #8492a6; font-size: 13px">{{ getDbTypeName(ds.type) }}</span>
                </el-option>
              </el-select>
            </div>

            <el-button
              type="primary"
              :disabled="multiple"
              @click="handleGenTable"
              :loading="generating"
              size="medium"
              class="primary-btn"
            >
              <template #icon><el-icon><MagicStick /></el-icon></template>
              <span v-if="generating">正在生成代码...</span>
              <span v-else>{{ ids.length > 0 ? `生成 ${ids.length} 个表的代码` : '生成代码' }}</span>
            </el-button>

            <el-button
              type="success"
              @click="createTable"
              size="medium"
              class="secondary-btn"
            >
              <template #icon><el-icon><DocumentAdd /></el-icon></template>
              SQL脚本生成
            </el-button>

            <el-button
              type="info"
              @click="openImportTable"
              size="medium"
              class="secondary-btn"
            >
              <template #icon><el-icon><Upload /></el-icon></template>
              导入数据表
            </el-button>

<!--            <el-button-->
<!--              type="danger"-->
<!--              @click="handleCompleteProject"-->
<!--              :loading="generatingProject"-->
<!--              size="medium"-->
<!--              class="highlight-btn"-->
<!--            >-->
<!--              <template #icon><el-icon><Rocket /></el-icon></template>-->
<!--              <span v-if="generatingProject">正在生成项目...</span>-->
<!--              <span v-else>{{ ids.length > 0 ? `一键生成完整项目 (${ids.length}表)` : '一键生成完整项目' }}</span>-->
<!--            </el-button>-->
          </div>

          <div class="secondary-actions">
            <el-dropdown trigger="click" class="more-dropdown" v-if="ids.length > 0">
              <el-button type="warning" size="medium">
          <template #icon><el-icon><More /></el-icon></template>
                批量操作<el-icon class="el-icon--right"><ArrowDown /></el-icon>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item @click.native="handleEditTable">
            <template #icon><el-icon><Edit /></el-icon></template>
                  <span>批量配置</span>
                </el-dropdown-item>
                <el-dropdown-item @click.native="handleBatch">
            <template #icon><el-icon><Setting /></el-icon></template>
                  <span>基础信息配置</span>
                </el-dropdown-item>
                <el-dropdown-item @click.native="handleDelete" divided>
            <template #icon><el-icon><Delete /></el-icon></template>
                  <span>删除选中表</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </el-dropdown>

            <div class="toolbar-tools">
              <el-tooltip content="筛选搜索" placement="top">
                <el-button
                  circle
                  @click="showSearch = !showSearch"
                  :type="showSearch ? 'primary' : ''"
                  size="medium"
                >
                  <template #icon><el-icon><Search /></el-icon></template>
                </el-button>
              </el-tooltip>
              <el-tooltip content="刷新数据" placement="top">
                <el-button circle @click="getList" size="medium">
          <template #icon><el-icon><Refresh /></el-icon></template>
        </el-button>
              </el-tooltip>
              <el-tooltip content="表格设置" placement="top">
                <el-button circle size="medium">
          <template #icon><el-icon><Setting /></el-icon></template>
        </el-button>
              </el-tooltip>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 数据表格 -->
    <div class="table-module">
      <el-card class="module-card">
        <div class="table-header">
          <div class="table-title">
            <div class="title-content">
              <el-icon><Grid /></el-icon>
              <span>数据表管理</span>
              <el-tag v-if="total > 0" type="info" size="mini" class="total-tag">
                共 {{ total }} 张表
              </el-tag>
            </div>
<!--            <div class="table-actions">-->
<!--              <el-radio-group v-model="viewMode" size="mini" class="view-toggle">-->
<!--                <el-radio-button label="table">-->
<!--                  <el-icon><Grid /></el-icon>-->
<!--                </el-radio-button>-->
<!--                <el-radio-button label="card">-->
<!--                  <i class="el-icon-menu"></i>-->
<!--                </el-radio-button>-->
<!--              </el-radio-group>-->
<!--            </div>-->
          </div>
        </div>

      <el-table
        v-loading="loading"
        :data="tableList"
        @selection-change="handleSelectionChange"
        class="gen-table"
        :empty-text="loading ? '加载中...' : '暂无数据'"
      >
        <el-table-column type="selection" width="50" align="center" />

        <el-table-column label="序号" width="60" align="center">
          <template #default="scope">
            <span class="table-index">
              {{ scope.$index + 1 }}
            </span>
          </template>
        </el-table-column>

        <el-table-column label="表信息" min-width="250">
          <template #default="scope">
            <div class="table-info" v-if="scope && scope.row">
              <div class="table-name">
                <div class="name-wrapper">
                  <el-icon class="table-icon"><Grid /></el-icon>
                  <span class="name-text">{{ scope.row.tableName || '-' }}</span>
                </div>
                <el-tag size="mini" type="primary" class="table-status">活跃</el-tag>
              </div>
              <div class="table-comment" v-if="scope.row.tableComment">
                <el-icon><Document /></el-icon>
                {{ scope.row.tableComment }}
              </div>
              <div class="table-meta">
                <el-tag size="mini" type="info" class="class-tag">
                  <el-icon><Cpu /></el-icon>
                  {{ scope.row.className || '-' }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="技术栈配置" width="220" align="center">
          <template #default="scope">
            <div class="tech-stack" v-if="scope && scope.row">
              <div class="tech-group">
                <span class="tech-label">后端:</span>
                <el-tag
                  size="mini"
                  :type="scope.row.backEnd === '0' ? 'primary' : 'success'"
                  class="tech-tag backend"
                >
                  <el-icon><Connection /></el-icon>
                  {{ scope.row.backEnd === '0' ? 'MyBatis' : 'MyBatis-Plus' }}
                </el-tag>
              </div>
              <div class="tech-group">
                <span class="tech-label">前端:</span>
                <el-tag
                  size="mini"
                  type="warning"
                  class="tech-tag frontend"
                >
                  <el-icon><View /></el-icon>
                  {{ getFrontEndName(scope.row.frontEnd) }}
                </el-tag>
              </div>
            </div>
          </template>
        </el-table-column>

        <el-table-column label="更新时间" width="160" align="center">
          <template #default="scope">
            <div class="update-time" v-if="scope && scope.row">
              <el-icon><Clock /></el-icon>
              {{ scope.row.updateTime || '-' }}
            </div>
          </template>
        </el-table-column>

        <el-table-column label="操作" width="300" align="center" fixed="right">
          <template #default="scope">
            <div class="table-actions" v-if="scope && scope.row">
              <el-button-group class="action-group">
                <el-tooltip content="预览代码" placement="top">
                  <el-button
                    type="primary"
                    size="small"
                    @click="handlePreview(scope.row)"
                    class="action-btn preview-btn"
                  >
                    <template #icon><el-icon><View /></el-icon></template>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="生成配置" placement="top">
                  <el-button
                    type="success"
                    size="small"
                    @click="handleEditTable(scope.row)"
                    class="action-btn config-btn"
                  >
                    <template #icon><el-icon><Setting /></el-icon></template>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="同步表结构" placement="top">
                  <el-button
                    type="warning"
                    size="small"
                    @click="handleSynchDb(scope.row)"
                    class="action-btn sync-btn"
                  >
                    <template #icon><el-icon><Refresh /></el-icon></template>
                  </el-button>
                </el-tooltip>
                <el-tooltip content="删除表" placement="top">
                  <el-button
                    type="danger"
                    size="small"
                    @click="handleDelete(scope.row)"
                    class="action-btn delete-btn"
                  >
                    <template #icon><el-icon><Delete /></el-icon></template>
                  </el-button>
                </el-tooltip>
              </el-button-group>

              <el-button
                type="primary"
                size="small"
                @click="handleGenTable(scope.row)"
                class="generate-btn"
                plain
              >
                <template #icon><el-icon><MagicStick /></el-icon></template>
                生成代码
              </el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination-wrapper">
        <pagination
          v-show="total > 0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
          layout="total, sizes, prev, pager, next, jumper"
        />
      </div>
    </el-card>
    </div>

    <!-- 预览界面 -->
    <el-dialog :title="preview.title" v-model="preview.open" width="80%" top="5vh" append-to-body class="scrollbar">
      <el-tabs v-model="preview.activeName">
        <el-tab-pane
          v-for="(value, key) in preview.data"
          :label="key.substring(key.lastIndexOf('/')+1,key.indexOf('.vm'))"
          :name="key.substring(key.lastIndexOf('/')+1,key.indexOf('.vm'))"
          :key="key"
        >
          <el-link :underline="false" v-clipboard:copy="value" v-clipboard:success="clipboardSuccess" style="float:right">
              <template #icon><el-icon><DocumentCopy /></el-icon></template>
              复制
            </el-link>
          <pre><code class="hljs" v-html="highlightedCode(value, key)"></code></pre>
        </el-tab-pane>
      </el-tabs>
    </el-dialog>
    <import-table ref="import" @ok="handleQuery" />

    <!--执行脚本-->
    <el-dialog title="执行脚本" v-model="centerDialogVisible" width="60%"  append-to-body top="5vh">
      <el-input type="textarea" v-model="noticeContent" placeholder="请输入创建数据表脚本" :rows="10"/>
      <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="createTableSubmit">确 定</el-button>
    <el-button @click="centerDialogVisible = false">取 消</el-button>
  </span>
    </el-dialog>

    <!-- 项目配置对话框 -->
    <el-dialog
      title="完整项目配置"
      v-model="projectConfigVisible"
      width="70%"
      append-to-body
      top="5vh"
      class="project-config-dialog"
    >
      <el-form
        ref="projectForm"
        :model="projectForm"
        :rules="projectRules"
        label-width="120px"
        class="project-form"
      >
        <div class="form-section">
          <h4 class="section-title">
            <el-icon><Setting /></el-icon>
            基础信息
          </h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="项目名称" prop="projectName">
                <el-input v-model="projectForm.projectName" placeholder="请输入项目名称" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="项目描述" prop="projectComment">
                <el-input v-model="projectForm.projectComment" placeholder="请输入项目描述" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="包名" prop="packageName">
                <el-input v-model="projectForm.packageName" placeholder="com.example.demo" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="类名前缀" prop="className">
                <el-input v-model="projectForm.className" placeholder="Demo" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="Group ID" prop="groupId">
                <el-input v-model="projectForm.groupId" placeholder="com.example" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="Artifact ID" prop="artifactId">
                <el-input v-model="projectForm.artifactId" placeholder="demo" />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="版本" prop="version">
                <el-input v-model="projectForm.version" placeholder="1.0.0" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="form-section">
          <h4 class="section-title">
            <el-icon><Connection /></el-icon>
            数据库配置
          </h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="数据库URL" prop="datasourceUrl">
                <el-input v-model="projectForm.datasourceUrl" placeholder="********************************" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="驱动类名" prop="driverClassName">
                <el-input v-model="projectForm.driverClassName" placeholder="com.mysql.cj.jdbc.Driver" />
              </el-form-item>
            </el-col>
          </el-row>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="用户名" prop="datasourceUsername">
                <el-input v-model="projectForm.datasourceUsername" placeholder="root" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="密码" prop="datasourcePassword">
                <el-input v-model="projectForm.datasourcePassword" type="password" placeholder="请输入数据库密码" show-password />
              </el-form-item>
            </el-col>
          </el-row>
        </div>

        <div class="form-section">
          <h4 class="section-title">
            <el-icon><Monitor /></el-icon>
            服务配置
          </h4>
          <el-row :gutter="20">
            <el-col :span="12">
              <el-form-item label="服务端口" prop="serverPort">
                <el-input-number v-model="projectForm.serverPort" :min="1000" :max="65535" placeholder="8080" />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="作者" prop="author">
                <el-input v-model="projectForm.author" placeholder="javaxiaobear" />
              </el-form-item>
            </el-col>
          </el-row>
        </div>
      </el-form>

      <div slot="footer" class="dialog-footer">
        <el-button @click="projectConfigVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmGenerateProject" :loading="generatingProject">
          <template #icon><el-icon><Rocket /></el-icon></template>
          {{ generatingProject ? '正在生成...' : '生成完整项目' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import download from "../utils/download";
import genApi from '@/api/gen'
import datasourceApi from '@/api/dataSource'
import importTable from './importTable.vue'
import hljs from 'highlight.js/lib/core'
import 'highlight.js/styles/github.css'
import pagination from "@/components/Pagination/index.vue";
import java from 'highlight.js/lib/languages/java'
import xml from 'highlight.js/lib/languages/xml'
import javascript from 'highlight.js/lib/languages/javascript'
import sql from 'highlight.js/lib/languages/sql'
import {
  ArrowRight,
  MagicStick,
  Grid,
  Check,
  Download,
  Search,
  Close,
  Calendar,
  Refresh,
  Setting,
  DocumentAdd,
  Upload,
  More,
  ArrowDown,
  Edit,
  Delete,
  Document,
  Cpu,
  Connection,
  View,
  Clock,
  DocumentCopy
} from '@element-plus/icons-vue'

hljs.registerLanguage("java", java);
hljs.registerLanguage("xml", xml);
hljs.registerLanguage("html", xml);
hljs.registerLanguage("vue", xml);
hljs.registerLanguage("javascript", javascript);
hljs.registerLanguage("sql", sql);

export default {
  name: "Gen",
  components: {
    importTable,
    pagination,
    ArrowRight,
    MagicStick,
    Grid,
    Check,
    Download,
    Search,
    Refresh,
    Setting,
    More,
    ArrowDown,
    Edit,
    Delete,
    Calendar,
    Clock,
    Document,
    View
  },
  data() {
    return {
      // 遮罩层
      loading: true,
      // 唯一标识符
      uniqueId: "",
      // 选中数组
      ids: [],
      // 选中表数组
      tableNames: [],
      noticeContent: '',
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 生成状态
      generating: false,
      // 项目生成状态
      generatingProject: false,
      // 项目配置对话框
      projectConfigVisible: false,
      // 项目配置表单
      projectForm: {
        projectName: 'demo-project',
        projectComment: '演示项目',
        packageName: 'com.example.demo',
        className: 'Demo',
        groupId: 'com.example',
        artifactId: 'demo',
        version: '1.0.0',
        author: 'javaxiaobear',
        driverClassName: 'com.mysql.cj.jdbc.Driver',
        datasourceUrl: '********************************',
        datasourceUsername: 'root',
        datasourcePassword: '123456',
        serverPort: 8080,
        jwtSecret: 'javaxiaobear-secret-key'
      },
      // 项目配置表单验证规则
      projectRules: {
        projectName: [
          { required: true, message: '请输入项目名称', trigger: 'blur' },
          { min: 2, max: 50, message: '长度在 2 到 50 个字符', trigger: 'blur' }
        ],
        projectComment: [
          { required: true, message: '请输入项目描述', trigger: 'blur' }
        ],
        packageName: [
          { required: true, message: '请输入包名', trigger: 'blur' },
          { pattern: /^[a-z][a-z0-9]*(\.[a-z][a-z0-9]*)*$/, message: '包名格式不正确', trigger: 'blur' }
        ],
        className: [
          { required: true, message: '请输入类名前缀', trigger: 'blur' },
          { pattern: /^[A-Z][a-zA-Z0-9]*$/, message: '类名必须以大写字母开头', trigger: 'blur' }
        ],
        groupId: [
          { required: true, message: '请输入Group ID', trigger: 'blur' }
        ],
        artifactId: [
          { required: true, message: '请输入Artifact ID', trigger: 'blur' },
          { pattern: /^[a-z][a-z0-9-]*$/, message: 'Artifact ID格式不正确', trigger: 'blur' }
        ],
        datasourceUrl: [
          { required: true, message: '请输入数据库URL', trigger: 'blur' }
        ],
        datasourceUsername: [
          { required: true, message: '请输入数据库用户名', trigger: 'blur' }
        ],
        datasourcePassword: [
          { required: true, message: '请输入数据库密码', trigger: 'blur' }
        ],
        serverPort: [
          { required: true, message: '请输入服务端口', trigger: 'blur' },
          { type: 'number', min: 1000, max: 65535, message: '端口范围1000-65535', trigger: 'blur' }
        ]
      },
      // 工具提示
      tooltipVisible: false,
      tooltipContent: '',
      // 视图模式
      viewMode: 'table',
      // 高级搜索
      showAdvanced: false,
      // 生成统计
      generatedCount: 0,
      // 数据源相关
      selectedDataSourceId: null,
      dataSourceList: [],
      // 总条数
      total: 0,
      // 表数据
      tableList: [],
      // 日期范围
      dateRange: "",
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 50,
        tableName: undefined,
        tableComment: undefined
      },
      // 预览参数
      preview: {
        open: false,
        title: "代码预览",
        data: {},
        activeName: "domain.java"
      },
      // 执行脚本
      centerDialogVisible: false,
      // 性能优化相关
      debounceTimer: null,
      lastSearchTime: 0,
      searchCache: new Map()
    };
  },

  computed: {
    // 计算属性优化
    hasSelectedTables() {
      return this.ids.length > 0
    },

    selectedTablesCount() {
      return this.ids.length
    },

    isAllSelected() {
      return this.tableList.length > 0 && this.ids.length === this.tableList.length
    },

    // 分页信息
    paginationInfo() {
      const start = (this.queryParams.pageNum - 1) * this.queryParams.pageSize + 1
      const end = Math.min(this.queryParams.pageNum * this.queryParams.pageSize, this.total)
      return `显示 ${start} 到 ${end} 条，共 ${this.total} 条`
    }
  },

  created() {
    this.getList();
    this.getDataSourceList();
  },
  activated() {
    const time = this.$route.query.t;
    if (time != null && time != this.uniqueId) {
      this.uniqueId = time;

      // 恢复分页参数
      const pageNum = Number(this.$route.query.pageNum) || 1;
      const pageSize = Number(this.$route.query.pageSize) || 50;

      console.log('页面激活，恢复分页参数:', { pageNum, pageSize });

      this.queryParams.pageNum = pageNum;
      this.queryParams.pageSize = pageSize;

      this.getList();
    }
  },

  beforeUnmount() {
    // 清理定时器
    if (this.debounceTimer) {
      clearTimeout(this.debounceTimer);
      this.debounceTimer = null;
    }

    // 清理缓存
    if (this.searchCache) {
      this.searchCache.clear();
    }

    console.log('Gen-index component cleanup completed');
  },

  methods: {
    /** 查询表集合 */
    getList() {
      // 生成缓存键
      const cacheKey = JSON.stringify(this.queryParams)
      const now = Date.now()

      // 检查缓存（5秒内的相同查询使用缓存）
      if (this.searchCache.has(cacheKey) && (now - this.lastSearchTime) < 5000) {
        const cachedData = this.searchCache.get(cacheKey)
        this.tableList = cachedData.rows
        this.total = cachedData.total
        return
      }

      this.loading = true
      this.lastSearchTime = now

      if (this.selectedDataSourceId) {
        this.queryParams.selectId = this.selectedDataSourceId
      }

      // 使用默认数据源查询
      genApi.listTable(this.queryParams).then(response => {
        this.tableList = response.rows || []
        this.total = response.total || 0
        this.loading = false

        // 缓存结果
        this.searchCache.set(cacheKey, {
          rows: this.tableList,
          total: this.total
        })

        // 限制缓存大小
        if (this.searchCache.size > 10) {
          const firstKey = this.searchCache.keys().next().value
          this.searchCache.delete(firstKey)
        }
      }).catch(error => {
        this.loading = false
        console.error('查询表列表失败:', error)
        this.$message.error('查询失败，请重试')
      })
      // 如果选择了数据源，使用指定数据源查询
      // if (this.selectedDataSourceId) {
      //   datasourceApi.listDbTableWithDataSource(this.selectedDataSourceId, this.queryParams).then(response => {
      //     this.tableList = response.rows;
      //     this.total = response.total;
      //     this.loading = false;
      //   }).catch(() => {
      //     this.loading = false;
      //   });
      // } else {
      //   // 使用默认数据源查询
      //   genApi.listTable(this.queryParams).then(response => {
      //     this.tableList = response.rows;
      //     this.total = response.total;
      //     this.loading = false;
      //   }).catch(() => {
      //     this.loading = false;
      //   });
      // }
    },
    /** 获取数据源列表 */
    getDataSourceList() {
      datasourceApi.listDatasource({}).then(response => {
        this.dataSourceList = response.rows || [];
      });
    },
    /** 复制代码成功 */
    clipboardSuccess() {
      this.$message.success("复制成功");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      // 清除之前的防抖定时器
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer)
      }

      // 设置防抖
      this.debounceTimer = setTimeout(() => {
        this.queryParams.pageNum = 1
        this.searchCache.clear() // 清空缓存
        this.getList()
      }, 300)
    },

    /** 实时搜索（输入时触发） */
    handleSearch() {
      // 清除之前的防抖定时器
      if (this.debounceTimer) {
        clearTimeout(this.debounceTimer)
      }

      // 设置防抖，延迟500ms执行搜索
      this.debounceTimer = setTimeout(() => {
        this.queryParams.pageNum = 1
        this.getList()
      }, 500)
    },
    /** 生成代码操作 */
    handleGenTable(row) {
      const tableNames = row.tableName || this.tableNames;
      if (tableNames == "") {
        this.$message.error("请选择要生成的数据");
        return;
      }

      // 添加生成动画
      this.generating = true;

      // 显示进度提示
      this.$message({
        message: '正在生成代码，请稍候...',
        type: 'info',
        duration: 2000
      });

      if(row.genType === "1") {
        genApi.genCode(row.tableName).then(response => {
          this.generating = false;
          this.$message.success("成功生成到自定义路径：" + row.genPath);
        }).catch(() => {
          this.generating = false;
        });
      } else {
        setTimeout(() => {
          this.generating = false;
          download.zip("/tool/gen/batchGenCode?tables=" + tableNames, "javaxiaobear代码生成");
          this.$message.success("代码生成完成！");
        }, 1500);
      }
    },
    /** 一键生成完整项目 */
    handleCompleteProject() {
      if (this.tableNames.length === 0) {
        this.$message.error("请选择要生成的数据表");
        return;
      }

      // 显示项目配置对话框
      this.projectConfigVisible = true;
    },
    /** 确认生成完整项目 */
    confirmGenerateProject() {
      this.$refs.projectForm.validate((valid) => {
        if (valid) {
          this.generatingProject = true;
          this.projectConfigVisible = false;

          // 显示进度提示
          this.$message({
            message: '正在生成完整项目，请稍候...',
            type: 'info',
            duration: 3000
          });

          const params = {
            tableNames: this.tableNames,
            projectConfig: this.projectForm
          };

          // 调用后端API生成完整项目
          this.$http.post('/tool/gen/generateCompleteProject', params, {
            responseType: 'blob'
          }).then(response => {
            this.generatingProject = false;

            // 创建下载链接
            const blob = new Blob([response.data], { type: 'application/zip' });
            const url = window.URL.createObjectURL(blob);
            const link = document.createElement('a');
            link.href = url;
            link.download = `${this.projectForm.projectName}-complete.zip`;
            document.body.appendChild(link);
            link.click();
            document.body.removeChild(link);
            window.URL.revokeObjectURL(url);

            this.$message.success("完整项目生成成功！");
          }).catch(error => {
            this.generatingProject = false;
            console.error('生成完整项目失败:', error);
            this.$message.error("生成完整项目失败，请重试");
          });
        }
      });
    },
    /** 同步数据库操作 */
    handleSynchDb(row) {
      const tableName = row.tableName;
      this.$confirm('确认要强制同步"' + tableName + '"表结构吗？').then(function() {
        return genApi.synchDb(tableName);
      }).then(() => {
        this.$message.success("同步成功");
      }).catch(() => {});
    },
    /** 打开导入表弹窗 */
    openImportTable() {
      this.$refs.import.show();
    },
    /** 导出*/
    handleExport(){
      const tableIds = this.ids;

      if (tableIds.length == 0) {
        this.$message.error("请选择要导出的数据");
        return;
      }
      download.download('/tool/gen/export?tableIds=' + tableIds, `数据字典.xlsx`)
    },
    handleBatch(){

    },
    createTable(){
      this.centerDialogVisible = true;
    },
    createTableSubmit(){
      const sql = this.noticeContent
      if (sql == ""){
        this.$message.error("请输入要执行创建表的sql");
        return;
      }
      genApi.createNewTable({ sql: sql }).then(res => {
        if (res.code === 200) {
          this.centerDialogVisible = false;
          this.noticeContent = ''
          this.$message.success("生成成功，请导入生成代码！");
          this.$emit("ok");
        }
      });
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 预览按钮 */
    handlePreview(row) {
      genApi.previewTable(row.tableId).then(response => {
        this.preview.data = response.data;
        this.preview.open = true;
        this.preview.activeName = "domain.java";
      });
    },
    /** 高亮显示 */
    highlightedCode(code, key) {
      const vmName = key.substring(key.lastIndexOf("/") + 1, key.indexOf(".vm"));
      var language = vmName.substring(vmName.indexOf(".") + 1, vmName.length);
      const result = hljs.highlight(language, code || "", true);
      return result.value || '&nbsp;';
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.tableId);
      this.tableNames = selection.map(item => item.tableName);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 修改按钮操作 */
    handleEditTable(row) {
      const tableId = row.tableId || this.ids[0];

      // 传递当前分页参数，以便返回时恢复分页状态
      this.$router.push({
        path: '/gen-edit',
        query: {
          tableId: tableId,
          pageNum: this.queryParams.pageNum,
          pageSize: this.queryParams.pageSize
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const tableIds = row.tableId || this.ids;

      this.$confirm('是否确认删除表编号为"' + tableIds + '"的数据项？').then(function() {
        return genApi.delTable(tableIds);
      }).then(() => {
        this.getList();
        this.$message.success("删除成功");
      }).catch(() => {});
    },
    /** 获取前端框架名称 */
    getFrontEndName(frontEnd) {
      if (frontEnd === undefined || frontEnd === null) {
        return 'Element UI Vue3';
      }
      const names = {
        '0': 'Element UI Vue2',
        '1': 'Ant Design Vue',
        '2': 'HTML',
        '3': 'Element UI Vue3'
      };
      return names[frontEnd] || 'Element UI Vue3';
    },
    /** 显示工具提示 */
    showTooltip(title, content) {
      this.tooltipContent = `${title}: ${content}`;
      this.tooltipVisible = true;
    },
    /** 隐藏工具提示 */
    hideTooltip() {
      this.tooltipVisible = false;
      this.tooltipContent = '';
    },
    /** 数据源切换 */
    handleDataSourceChange(dataSourceId) {
      this.selectedDataSourceId = dataSourceId;
      localStorage.setItem('selectedDataSourceId', dataSourceId); // 保存到localStorage
      this.queryParams.pageNum = 1;
      this.getList();

      if (dataSourceId) {
        const dataSource = this.dataSourceList.find(ds => ds.id === dataSourceId);
        this.$message.success(`已切换到数据源: ${dataSource.name}`);
      } else {
        this.$message.success('已切换到默认数据源');
      }
    },
    /** 获取数据库类型名称 */
    getDbTypeName(type) {
      const names = {
        '0': 'MySQL',
        '1': 'Oracle',
        '2': 'SQL Server',
        '3': 'PostgreSQL',
        '4': 'ClickHouse',
        '5': 'SQLite',
        '6': 'DB2',
        '7': '达梦'
      };
      return names[type] || 'Unknown';
    }
  }
};
</script>

<style lang="scss" scoped>
.gen-container {
  padding: 20px;
  background: var(--bg-secondary);
  min-height: calc(100vh - 64px);
}

// 页面头部
.page-header {
  position: relative;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  margin-bottom: var(--spacing-xl);
  overflow: hidden;

  .header-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;

    .header-pattern {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
      animation: float 20s ease-in-out infinite;
    }
  }

  .header-content {
    position: relative;
    z-index: 1;
    max-width: 1400px;
    margin: 0 auto;
    padding: var(--spacing-xl) var(--spacing-lg);
    display: flex;
    justify-content: space-between;
    align-items: flex-start;

    @media (max-width: 768px) {
      flex-direction: column;
      gap: var(--spacing-lg);
      padding: var(--spacing-lg) var(--spacing-md);
    }
  }

  .header-info {
    flex: 1;

    .page-breadcrumb {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      margin-bottom: var(--spacing-md);
      font-size: 0.9rem;
      opacity: 0.8;

      .breadcrumb-item {
        &.active {
          color: #fff;
          font-weight: 500;
        }
      }

      i {
        font-size: 0.8rem;
      }
    }

    .page-title {
      font-size: 2.5rem;
      font-weight: 800;
      margin: 0 0 var(--spacing-md);
      display: flex;
      align-items: center;
      gap: var(--spacing-md);

      .title-icon {
        width: 60px;
        height: 60px;
        background: rgba(255, 255, 255, 0.2);
        border-radius: var(--border-radius-large);
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);

        i {
          font-size: 28px;
          color: #fff;
        }
      }
    }

    .page-description {
      font-size: 1.1rem;
      line-height: 1.6;
      opacity: 0.9;
      max-width: 600px;
    }
  }

  .header-stats {
    display: flex;
    gap: var(--spacing-md);

    @media (max-width: 768px) {
      width: 100%;
      justify-content: space-between;
    }

    .stat-card {
      background: rgba(255, 255, 255, 0.15);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.2);
      border-radius: var(--border-radius-large);
      padding: var(--spacing-lg);
      min-width: 120px;
      text-align: center;
      transition: all 0.3s ease;

      &:hover {
        transform: translateY(-5px);
        background: rgba(255, 255, 255, 0.2);
      }

      &.primary .stat-icon {
        background: linear-gradient(135deg, #667eea, #764ba2);
      }

      &.success .stat-icon {
        background: linear-gradient(135deg, #4facfe, #00f2fe);
      }

      &.info .stat-icon {
        background: linear-gradient(135deg, #a8edea, #fed6e3);
      }

      .stat-icon {
        width: 40px;
        height: 40px;
        margin: 0 auto var(--spacing-sm);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        i {
          font-size: 18px;
          color: white;
        }
      }

      .stat-content {
        .stat-number {
          font-size: 1.8rem;
          font-weight: 700;
          color: white;
          margin-bottom: var(--spacing-xs);
        }

        .stat-label {
          font-size: 0.85rem;
          opacity: 0.8;
        }
      }
    }
  }
}

// 搜索区域
.search-section {
  margin-bottom: var(--spacing-lg);

  .search-card {
    border: none;
    box-shadow: var(--shadow-light);

    .search-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-lg);
      padding-bottom: var(--spacing-md);
      border-bottom: 1px solid var(--border-light);

      .search-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);

        i {
          color: var(--primary-color);
        }
      }

      .close-search {
        color: var(--text-secondary);

        &:hover {
          color: var(--primary-color);
        }
      }
    }

    .search-form {
      .form-row {
        display: flex;
        gap: var(--spacing-lg);
        margin-bottom: var(--spacing-lg);

        @media (max-width: 768px) {
          flex-direction: column;
          gap: var(--spacing-md);
        }

        .form-item {
          flex: 1;
          margin-bottom: 0;

          .search-input {
            width: 100%;
          }

          .date-picker {
            width: 100%;
          }
        }
      }

      .form-actions {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        padding-top: var(--spacing-md);
        border-top: 1px solid var(--border-light);

        .action-btn {
          padding: var(--spacing-sm) var(--spacing-lg);
        }

        .advanced-btn {
          margin-left: auto;
          color: var(--text-secondary);

          &:hover {
            color: var(--primary-color);
          }
        }
      }
    }
  }
}

// 工具栏区域
.toolbar-section {
  margin-bottom: var(--spacing-lg);

  .toolbar-card {
    border: none;
    box-shadow: var(--shadow-light);

    .toolbar-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-lg);
      padding-bottom: var(--spacing-md);
      border-bottom: 1px solid var(--border-light);

      .toolbar-title {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
        font-size: 1.1rem;
        font-weight: 600;
        color: var(--text-primary);

        i {
          color: var(--primary-color);
        }
      }

      .selection-info {
        .el-tag {
          background: rgba(245, 112, 112, 0.1);
          border-color: var(--primary-color);
          color: var(--primary-color);
        }
      }
    }

    .toolbar-content {
      display: flex;
      justify-content: space-between;
      align-items: center;
      gap: var(--spacing-lg);

      @media (max-width: 768px) {
        flex-direction: column;
        gap: var(--spacing-md);
      }

      .primary-actions {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);

        .datasource-selector {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);

          .datasource-label {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--text-primary);
            white-space: nowrap;
          }

          .datasource-select {
            min-width: 220px;

            :deep(.el-input__inner) {
              border-radius: var(--border-radius-base);
              border: 2px solid var(--border-light);
              transition: all 0.3s ease;

              &:focus {
                border-color: var(--primary-color);
                box-shadow: 0 0 0 2px rgba(245, 112, 112, 0.2);
              }
            }
          }
        }

        .primary-btn {
          background: var(--primary-gradient);
          border: none;
          padding: var(--spacing-md) var(--spacing-xl);
          font-weight: 600;
          box-shadow: 0 4px 12px rgba(245, 112, 112, 0.3);

          &:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 16px rgba(245, 112, 112, 0.4);
          }
        }

        .secondary-btn {
          padding: var(--spacing-md) var(--spacing-lg);
          font-weight: 500;
        }
      }

      .secondary-actions {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);

        .more-dropdown {
          .el-button {
            padding: var(--spacing-md) var(--spacing-lg);
          }
        }

        .toolbar-tools {
          display: flex;
          gap: var(--spacing-sm);
          margin-left: var(--spacing-md);
          padding-left: var(--spacing-md);
          border-left: 1px solid var(--border-light);
        }
      }
    }
  }
}

// 表格区域
.table-section {
  .table-card {
    border: none;
    box-shadow: var(--shadow-light);

    .table-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: var(--spacing-lg);
      padding-bottom: var(--spacing-md);
      border-bottom: 1px solid var(--border-light);

      .table-title {
        .title-content {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);
          font-size: 1.2rem;
          font-weight: 600;
          color: var(--text-primary);

          i {
            color: var(--primary-color);
            font-size: 1.3rem;
          }

          .total-tag {
            background: rgba(245, 112, 112, 0.1);
            border-color: var(--primary-color);
            color: var(--primary-color);
          }
        }
      }

      .table-actions {
        .view-toggle {
          .el-radio-button__inner {
            padding: var(--spacing-xs) var(--spacing-sm);
            border-radius: var(--border-radius-small);
          }
        }
      }
    }
  }
}

  .gen-table {
    :deep(.el-table__row) {
      transition: all 0.3s ease;

      &:hover {
        background: rgba(245, 112, 112, 0.03) !important;
        transform: translateY(-1px);
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
      }
    }

    .table-index {
      font-weight: 600;
      color: var(--primary-color);
      background: rgba(245, 112, 112, 0.1);
      border-radius: 50%;
      width: 24px;
      height: 24px;
      display: inline-flex;
      align-items: center;
      justify-content: center;
      font-size: 12px;
    }

    .table-info {
      .table-name {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-sm);

        .name-wrapper {
          display: flex;
          align-items: center;
          gap: var(--spacing-sm);

          .table-icon {
            color: var(--primary-color);
            font-size: 1.1rem;
          }

          .name-text {
            font-weight: 600;
            color: var(--text-primary);
            font-size: 1rem;
          }
        }

        .table-status {
          background: linear-gradient(135deg, #4facfe, #00f2fe);
          border: none;
          color: white;
          font-size: 0.75rem;
        }
      }

      .table-comment {
        display: flex;
        align-items: center;
        gap: var(--spacing-xs);
        color: var(--text-secondary);
        font-size: 0.9rem;
        margin-bottom: var(--spacing-sm);
        line-height: 1.4;

        i {
          color: var(--primary-color);
          font-size: 0.8rem;
        }
      }

      .table-meta {
        .class-tag {
          background: rgba(103, 194, 58, 0.1);
          border-color: #67c23a;
          color: #67c23a;

          i {
            margin-right: var(--spacing-xs);
          }
        }
      }
    }

    .tech-stack {
      display: flex;
      flex-direction: column;
      gap: var(--spacing-md);

      .tech-group {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);

        .tech-label {
          font-size: 0.8rem;
          color: var(--text-secondary);
          min-width: 35px;
        }

        .tech-tag {
          margin: 0;
          display: flex;
          align-items: center;
          gap: var(--spacing-xs);
          padding: var(--spacing-xs) var(--spacing-sm);

          &.backend {
            background: rgba(64, 158, 255, 0.1);
            border-color: #409eff;
            color: #409eff;

            &.success {
              background: rgba(103, 194, 58, 0.1);
              border-color: #67c23a;
              color: #67c23a;
            }
          }

          &.frontend {
            background: rgba(230, 162, 60, 0.1);
            border-color: #e6a23c;
            color: #e6a23c;
          }

          i {
            font-size: 0.8rem;
          }
        }
      }
    }

    .update-time {
      display: flex;
      align-items: center;
      justify-content: center;
      gap: var(--spacing-xs);
      color: var(--text-secondary);
      font-size: 0.9rem;

      i {
        color: var(--primary-color);
      }
    }

    .table-actions {
      display: flex;
      align-items: center;
      gap: var(--spacing-sm);
      justify-content: center;

      .action-group {
        display: flex;
        border-radius: var(--border-radius-base);
        overflow: hidden;
        box-shadow: var(--shadow-light);

        .action-btn {
          border-radius: 0;
          border-right: 1px solid rgba(255, 255, 255, 0.2);
          transition: all 0.3s ease;

          &:last-child {
            border-right: none;
          }

          &:hover {
            transform: translateY(-1px);
            z-index: 1;
          }

          &.preview-btn {
            background: #409eff;
            border-color: #409eff;
            color: white;

            &:hover {
              background: #66b1ff;
            }
          }

          &.config-btn {
            background: #67c23a;
            border-color: #67c23a;
            color: white;

            &:hover {
              background: #85ce61;
            }
          }

          &.config-btn {
            background: #67c23a;
            border-color: #67c23a;
            color: white;

            &:hover {
              background: #85ce61;
            }
          }

          &.sync-btn {
            background: #e6a23c;
            border-color: #e6a23c;
            color: white;

            &:hover {
              background: #ebb563;
            }
          }
        }
      }

      .generate-btn {
        background: var(--primary-gradient);
        border: none;
        color: white;
        padding: var(--spacing-xs) var(--spacing-md);
        border-radius: var(--border-radius-base);
        font-weight: 500;
        box-shadow: 0 2px 8px rgba(245, 112, 112, 0.3);
        transition: all 0.3s ease;

        &:hover {
          transform: translateY(-2px);
          box-shadow: 0 4px 12px rgba(245, 112, 112, 0.4);
        }

        &:active {
          transform: translateY(-1px);
        }
      }
    }
  }

  .pagination-wrapper {
    margin-top: var(--spacing-lg);
    display: flex;
    justify-content: center;
  }

// 对话框样式增强
:deep(.el-dialog) {
  .el-dialog__header {
    background: var(--bg-tertiary);
    border-bottom: 1px solid var(--border-light);
  }

  .el-dialog__body {
    max-height: 70vh;
    overflow-y: auto;
  }
}

// 项目配置对话框样式
.project-config-dialog {
  :deep(.el-dialog) {
    border-radius: 12px;
    overflow: hidden;
  }

  :deep(.el-dialog__header) {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px 24px;

    .el-dialog__title {
      font-size: 18px;
      font-weight: 600;
    }
  }

  :deep(.el-dialog__body) {
    padding: 24px;
    background: #fafbfc;
  }

  .project-form {
    .form-section {
      background: white;
      border-radius: 8px;
      padding: 20px;
      margin-bottom: 20px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);

      .section-title {
        display: flex;
        align-items: center;
        gap: 8px;
        margin: 0 0 16px 0;
        font-size: 16px;
        font-weight: 600;
        color: #2c3e50;
        border-bottom: 2px solid #e1e8ed;
        padding-bottom: 8px;

        .el-icon {
          color: #667eea;
        }
      }

      :deep(.el-form-item) {
        margin-bottom: 16px;

        .el-form-item__label {
          font-weight: 500;
          color: #34495e;
        }

        .el-input__inner,
        .el-input-number__inner {
          border-radius: 6px;
          border: 1px solid #dcdfe6;
          transition: all 0.3s ease;

          &:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.1);
          }
        }
      }
    }
  }

  :deep(.dialog-footer) {
    padding: 16px 24px;
    background: white;
    border-top: 1px solid #e1e8ed;

    .el-button {
      border-radius: 6px;
      padding: 10px 20px;
      font-weight: 500;

      &.el-button--primary {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;

        &:hover {
          background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }
      }
    }
  }
}

// 一键生成按钮样式
.highlight-btn {
  background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%) !important;
  border: none !important;
  color: white !important;
  font-weight: 600 !important;
  box-shadow: 0 4px 15px rgba(255, 107, 107, 0.3) !important;
  transition: all 0.3s ease !important;

  &:hover {
    background: linear-gradient(135deg, #ff5252 0%, #d63031 100%) !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 6px 20px rgba(255, 107, 107, 0.4) !important;
  }

  &:active {
    transform: translateY(0) !important;
  }

  &.is-loading {
    background: linear-gradient(135deg, #ff9999 0%, #ff7675 100%) !important;
  }
}

// 代码预览样式
.scrollbar {
  :deep(.el-tabs__content) {
    max-height: 60vh;
    overflow-y: auto;
  }

  pre {
    background: #f6f8fa;
    border-radius: var(--border-radius-base);
    padding: var(--spacing-md);
    margin: 0;
    font-size: 13px;
    line-height: 1.5;
  }
}

// 响应式适配
@media (max-width: 768px) {
  .gen-container {
    padding: 0 var(--spacing-sm);
  }

  .table-card {
    .gen-table {
      .table-actions {
        flex-direction: column;
        gap: var(--spacing-xs);

        .action-btn {
          width: 100%;
          justify-content: center;
        }
      }
    }
  }
}
</style>
