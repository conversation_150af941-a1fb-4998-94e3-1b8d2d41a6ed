<template>
  <div class="index-container">
    <!-- Hero Section -->
    <section class="hero">
      <div class="hero-background">
        <div class="hero-pattern"></div>
      </div>
      <div class="hero-content">
        <div class="hero-badge fade-in-down">
          <el-icon><Star /></el-icon>
          <span>开源免费的代码生成器</span>
        </div>
        <img
          v-if="data.heroImage"
          :src="data.heroImage"
          class="hero-image fade-in-up"
          alt="小熊代码生成器"
        >
        <h1 class="main-title fade-in-up">
          让代码生成
          <span class="highlight-text">更简单</span>
        </h1>
        <p class="description fade-in-up">
          基于若依RuoYi-Vue的企业级代码生成器，支持多种主流技术栈，智能分析数据库结构，
          <br>一键生成前后端代码，让开发效率提升10倍
        </p>
        <div class="hero-actions fade-in-up">
          <el-button
            type="primary"
            size="large"
            class="action-button primary"
            @click="handleUse"
            @mouseenter="handleButtonHover('primary')"
            @mouseleave="handleButtonLeave('primary')"
          >
            <el-icon :class="{ 'animate-bounce': primaryHovered }"><ArrowRight /></el-icon>
            {{ data.actionText }}
          </el-button>
          <el-button
            size="large"
            class="action-button secondary"
            @click="handleDemo"
            @mouseenter="handleButtonHover('secondary')"
            @mouseleave="handleButtonLeave('secondary')"
          >
            <el-icon :class="{ 'animate-pulse': secondaryHovered }"><VideoPlay /></el-icon>
            在线演示
          </el-button>
        </div>
        <div class="hero-stats fade-in-up">
          <div class="stat-item">
            <div class="stat-number">10,000+</div>
            <div class="stat-label">开发者使用</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">12+</div>
            <div class="stat-label">数据库支持</div>
          </div>
          <div class="stat-item">
            <div class="stat-number">98%</div>
            <div class="stat-label">代码可用率</div>
          </div>
        </div>
      </div>
    </section>

    <!-- Features Section -->
    <section class="features-section">
      <div class="section-header">
        <h2 class="section-title">核心特性</h2>
        <p class="section-subtitle">强大的功能，简单的操作</p>
      </div>
      <div class="features-grid">
        <div
          v-for="(feature, index) in features"
          :key="index"
          class="feature-card"
          :class="`fade-in-up delay-${index}`"
          @mouseenter="handleFeatureHover(index)"
          @mouseleave="handleFeatureLeave(index)"
          @click="handleFeatureClick(feature)"
        >
          <div class="feature-icon" :class="{ 'icon-hover': hoveredFeature === index }">
            <el-icon><component :is="getFeatureIcon(index)" /></el-icon>
          </div>
          <h3 class="feature-title">{{ feature.title }}</h3>
          <p class="feature-description">{{ feature.details }}</p>
          <div class="feature-arrow" :class="{ 'arrow-hover': hoveredFeature === index }">
            <el-icon><ArrowRight /></el-icon>
          </div>
        </div>
      </div>
    </section>

    <!-- Why Choose Us Section -->
    <section class="why-section">
      <div class="section-header">
        <h2 class="section-title">为什么选择小熊代码生成器？</h2>
        <p class="section-subtitle">专为企业级开发而设计，让代码生成更智能、更高效</p>
      </div>
      <div class="why-grid">
        <div class="why-item" v-for="(item, index) in whyChooseUs" :key="index">
          <div class="why-number">{{ index + 1 }}</div>
          <div class="why-content">
            <h3 class="why-title">{{ item.title }}</h3>
            <p class="why-description">{{ item.description }}</p>
          </div>
        </div>
      </div>
    </section>

    <!-- Technology Stack Section -->
    <section class="tech-section">
      <div class="section-header">
        <h2 class="section-title">技术栈支持</h2>
        <p class="section-subtitle">支持主流的开发技术栈</p>
      </div>
      <div class="tech-grid">
        <div
          v-for="(tech, index) in techStack"
          :key="tech.name"
          class="tech-item"
          :class="{ 'tech-hover': hoveredTech === index }"
          @mouseenter="handleTechHover(index)"
          @mouseleave="handleTechLeave(index)"
          @click="handleTechClick(tech)"
        >
          <div class="tech-icon">
            <el-icon><component :is="getTechIcon(tech.icon)" /></el-icon>
          </div>
          <span class="tech-name">{{ tech.name }}</span>
          <div class="tech-tooltip" v-if="hoveredTech === index">
            {{ tech.description || `${tech.name} 技术栈` }}
          </div>
        </div>
      </div>
    </section>

    <!-- Contact Section -->
    <section class="contact-section">
      <div class="contact-container">
        <div class="contact-content">
          <div class="contact-info">
            <h2 class="contact-title">开始使用小熊代码生成器</h2>
            <p class="contact-description">
              立即体验智能代码生成的强大功能，让开发变得更简单、更高效
            </p>
            <div class="contact-features">
              <div class="contact-feature">
                <el-icon><Star /></el-icon>
                <span>免费开源</span>
              </div>
              <div class="contact-feature">
                <el-icon><DocumentAdd /></el-icon>
                <span>详细文档</span>
              </div>
              <div class="contact-feature">
                <el-icon><Connection /></el-icon>
                <span>社区支持</span>
              </div>
            </div>
          </div>
          <div class="contact-actions">
            <el-button
              type="primary"
              size="large"
              class="contact-button"
              @click="handleUse"
            >
              <el-icon><ArrowRight /></el-icon>
              立即开始
            </el-button>
            <el-button
              size="large"
              class="contact-button secondary"
              @click="handleGithub"
            >
              <el-icon><Link /></el-icon>
              查看源码
            </el-button>
          </div>
        </div>
      </div>
    </section>

    <!-- Footer -->
    <footer class="footer">
      <div class="footer-container">
        <div class="footer-content">
          <div class="footer-section">
            <h3 class="footer-title">小熊代码生成器</h3>
            <p class="footer-description">
              基于若依RuoYi-Vue的企业级代码生成器，让开发更简单、更高效
            </p>
            <div class="footer-social">
              <a href="#" class="social-link" @click="handleGithub">
                <el-icon><Link /></el-icon>
                GitHub
              </a>
            </div>
          </div>
          <div class="footer-section">
            <h4 class="footer-subtitle">产品</h4>
            <ul class="footer-links">
              <li><a href="#" @click="handleUse">代码生成</a></li>
              <li><a href="#" @click="handleDemo">在线演示</a></li>
              <li><a href="#">使用文档</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4 class="footer-subtitle">支持</h4>
            <ul class="footer-links">
              <li><a href="#">帮助中心</a></li>
              <li><a href="#">常见问题</a></li>
              <li><a href="#">联系我们</a></li>
            </ul>
          </div>
          <div class="footer-section">
            <h4 class="footer-subtitle">关于</h4>
            <ul class="footer-links">
              <li><a href="#">关于我们</a></li>
              <li><a href="#">开源协议</a></li>
              <li><a href="#">更新日志</a></li>
            </ul>
          </div>
        </div>
        <div class="footer-bottom">
          <p class="footer-copyright">
            © 2024 小熊代码生成器. All rights reserved. 基于 MIT 协议开源
          </p>
        </div>
      </div>
    </footer>
  </div>
</template>

<script>
import logo from '@/assets/logo.png'
import { 
  Star, 
  ArrowRight, 
  VideoPlay, 
  MagicStick, 
  DocumentAdd, 
  FolderOpened,
  Cpu,
  Connection,
  Link,
  View,
  Menu,
  Grid,
  Coin,
  DataBoard
} from '@element-plus/icons-vue'
import { getNewIconName, isOldElementIcon } from '@/utils/icon-mapping'

export default {
  name: "index",
  components: {
    Star,
    ArrowRight,
    VideoPlay,
    MagicStick,
    DocumentAdd,
    FolderOpened,
    Cpu,
    Connection,
    Link,
    View,
    Menu,
    Grid,
    Coin,
    DataBoard
  },
  data() {
    return {
      data: {
        heroImage: logo,
        heroText: '小熊代码生成器',
        actionText: '立即开始',
        description: '基于若依RuoYi-Vue的强大代码生成器，支持MyBatis、MyBatis-Plus后端框架，Element UI、Ant Design Vue前端框架，可自由组合生成高质量代码，提升开发效率。'
      },
      techStack: [
        { name: 'Spring Boot', icon: 'Cpu', description: '企业级Java开发框架' },
        { name: 'MyBatis', icon: 'Connection', description: '优秀的持久层框架' },
        { name: 'MyBatis-Plus', icon: 'Link', description: 'MyBatis增强工具' },
        { name: 'Vue.js', icon: 'View', description: '渐进式JavaScript框架' },
        { name: 'Element UI', icon: 'Menu', description: 'Vue.js桌面端组件库' },
        { name: 'Ant Design', icon: 'Grid', description: '企业级UI设计语言' },
        { name: 'MySQL', icon: 'Coin', description: '流行的关系型数据库' },
        { name: 'PostgreSQL', icon: 'DataBoard', description: '先进的开源数据库' }
      ],
      whyChooseUs: [
        {
          title: '智能化代码生成',
          description: '基于AI算法智能分析数据库结构，自动生成符合企业级规范的高质量代码，减少90%的重复工作'
        },
        {
          title: '多框架支持',
          description: '支持Spring Boot、MyBatis、Vue.js等主流技术栈，可灵活组合，满足不同项目需求'
        },
        {
          title: '开箱即用',
          description: '生成的代码包含完整的CRUD操作、权限控制、数据校验等功能，可直接部署到生产环境'
        },
        {
          title: '持续更新',
          description: '紧跟技术发展趋势，定期更新模板和功能，确保生成的代码始终采用最新的最佳实践'
        }
      ],
      primaryHovered: false,
      secondaryHovered: false,
      hoveredFeature: -1,
      hoveredTech: -1
    }
  },
  computed: {
    features() {
      return [
        {
          title: '智能代码生成',
          details: '基于数据表结构智能分析，支持MyBatis、MyBatis-Plus等主流框架，自动生成Controller、Service、Mapper等完整的业务代码',
          icon: 'MagicStick'
        },
        {
          title: '多数据库支持',
          details: '兼容MySQL、PostgreSQL、Oracle、SQL Server等12种主流数据库，自动适配不同数据库的数据类型和语法特性',
          icon: 'DocumentAdd'
        },
        {
          title: '开箱即用',
          details: '生成的代码遵循企业级开发规范，包含完整的CRUD操作、数据校验、异常处理，可直接用于生产环境',
          icon: 'FolderOpened'
        }
      ]
    }
  },
  mounted() {
    this.initAnimations()
  },
  methods: {
    handleUse() {
      // 添加点击反馈
      this.$message({
        message: '正在跳转到代码生成页面...',
        type: 'success',
        duration: 1500
      })

      // 添加页面切换动画
      document.body.classList.add('page-transition')

      setTimeout(() => {
        this.$router.push({
          path: "/generate/index",
          query: { pageNum: this.$route.query.pageNum }
        })
      }, 300)
    },
    handleDemo() {
      this.$message({
        message: '演示功能开发中，敬请期待！',
        type: 'info',
        duration: 2000
      })
    },
    handleGithub() {
      this.$message({
        message: '正在跳转到GitHub仓库...',
        type: 'success',
        duration: 1500
      })
      // 这里可以替换为实际的GitHub地址
      window.open('https://github.com/javaxiaobear/xiaobear-gen', '_blank')
    },
    handleButtonHover(type) {
      if (type === 'primary') {
        this.primaryHovered = true
      } else {
        this.secondaryHovered = true
      }
    },
    handleButtonLeave(type) {
      if (type === 'primary') {
        this.primaryHovered = false
      } else {
        this.secondaryHovered = false
      }
    },
    handleFeatureHover(index) {
      this.hoveredFeature = index
    },
    handleFeatureLeave(index) {
      this.hoveredFeature = -1
    },
    handleFeatureClick(feature) {
      this.$message({
        message: `了解更多关于${feature.title}的信息`,
        type: 'info',
        duration: 2000
      })
    },
    handleTechHover(index) {
      this.hoveredTech = index
    },
    handleTechLeave(index) {
      this.hoveredTech = -1
    },
    handleTechClick(tech) {
      this.$message({
        message: `${tech.name}: ${tech.description}`,
        type: 'success',
        duration: 3000
      })
    },
    getFeatureIcon(index) {
      const icons = ['MagicStick', 'DocumentAdd', 'FolderOpened']
      return icons[index] || 'Document'
    },
    getTechIcon(iconName) {
      return iconName
    },
    initAnimations() {
      // 初始化动画
      const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
          if (entry.isIntersecting) {
            entry.target.classList.add('animate')
          }
        })
      }, {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
      })

      document.querySelectorAll('.fade-in-up, .fade-in-down').forEach(el => {
        observer.observe(el)
      })
    }
  }
}
</script>

<style scoped lang="scss">
:root {
  --primary-color: #f57070;
  --primary-gradient: linear-gradient(135deg, #f57070 0%, #ff8e8e 100%);
  --bg-primary: #ffffff;
  --bg-secondary: #f8fafc;
  --text-primary: #1a202c;
  --text-regular: #4a5568;
  --text-secondary: #718096;
  --border-base: #e2e8f0;
  --border-radius-base: 8px;
  --border-radius-large: 16px;
  --border-radius-small: 4px;
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  --spacing-xl: 48px;
  --shadow-light: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-base: 0 4px 16px rgba(0, 0, 0, 0.1);
  --shadow-dark: 0 8px 32px rgba(0, 0, 0, 0.15);
}

.index-container {
  margin: 0;
  padding: 0;
  min-height: 100vh;
  background: var(--bg-secondary);
  overflow-x: hidden;
}

// Hero Section
.hero {
  position: relative;
  min-height: 100vh;
  display: flex;
  align-items: center;
  background: linear-gradient(135deg, #ffffff 0%, #f8fafc 100%);
  overflow: hidden;

  .hero-background {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    z-index: 0;

    .hero-pattern {
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background-image:
        radial-gradient(circle at 25% 25%, rgba(245, 112, 112, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 142, 142, 0.1) 0%, transparent 50%);
      animation: float 20s ease-in-out infinite;
    }
  }

  .hero-content {
    position: relative;
    z-index: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0;
    text-align: center;

    .hero-badge {
      display: inline-flex;
      align-items: center;
      gap: var(--spacing-xs);
      padding: var(--spacing-xs) var(--spacing-md);
      background: rgba(245, 112, 112, 0.1);
      border: 1px solid rgba(245, 112, 112, 0.2);
      border-radius: 50px;
      color: var(--primary-color);
      font-size: 14px;
      font-weight: 500;
      margin-bottom: var(--spacing-lg);

      i {
        color: #ffd700;
      }
    }

    .hero-image {
      height: 120px;
      margin-bottom: var(--spacing-lg);
      transition: transform 0.6s cubic-bezier(0.4, 0, 0.2, 1);
      filter: drop-shadow(0 10px 20px rgba(0, 0, 0, 0.1));

      &:hover {
        transform: scale(1.05) rotate(5deg);
      }
    }

    .main-title {
      font-size: clamp(2.5rem, 5vw, 4rem);
      font-weight: 800;
      margin: var(--spacing-lg) 0;
      color: var(--text-primary);
      line-height: 1.2;
      letter-spacing: -0.02em;
      
      .highlight-text {
        background: var(--primary-gradient);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        position: relative;
        
        &::after {
          content: '';
          position: absolute;
          bottom: -4px;
          left: 0;
          right: 0;
          height: 3px;
          background: var(--primary-gradient);
          border-radius: 2px;
          animation: highlight-expand 0.8s ease-out 0.5s both;
        }
      }
    }

    .description {
      max-width: 600px;
      margin: 0 auto var(--spacing-xl);
      font-size: 1.2rem;
      line-height: 1.8;
      color: var(--text-regular);
    }

    .hero-actions {
      display: flex;
      justify-content: center;
      gap: var(--spacing-md);
      margin-bottom: var(--spacing-xl);

      @media (max-width: 768px) {
        flex-direction: column;
        align-items: center;
      }

      .action-button {
        padding: var(--spacing-md) var(--spacing-xl);
        border-radius: 50px;
        font-size: 1.1rem;
        font-weight: 600;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        overflow: hidden;

        &::before {
          content: '';
          position: absolute;
          top: 0;
          left: -100%;
          width: 100%;
          height: 100%;
          background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
          transition: left 0.5s ease;
        }

        &:hover::before {
          left: 100%;
        }

        &.primary {
          background: var(--primary-gradient);
          border: none;
          color: white;
          box-shadow: 0 4px 15px rgba(245, 112, 112, 0.3);

          &:hover {
            transform: translateY(-3px) scale(1.02);
            box-shadow: 0 8px 25px rgba(245, 112, 112, 0.4);
          }

          &:active {
            transform: translateY(-1px) scale(1.01);
          }

          i {
            margin-left: var(--spacing-xs);
            transition: all 0.3s ease;

            &.animate-bounce {
              animation: bounce 0.6s ease-in-out;
            }
          }
        }

        &.secondary {
          background: white;
          border: 2px solid var(--border-base);
          color: var(--text-primary);

          &:hover {
            border-color: var(--primary-color);
            color: var(--primary-color);
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 4px 15px rgba(245, 112, 112, 0.2);
          }

          &:active {
            transform: translateY(-1px) scale(1.01);
          }

          i {
            margin-right: var(--spacing-xs);
            transition: all 0.3s ease;

            &.animate-pulse {
              animation: pulse-icon 1s ease-in-out infinite;
            }
          }
        }
      }
    }

    .hero-stats {
      display: flex;
      justify-content: center;
      gap: var(--spacing-xl);

      @media (max-width: 768px) {
        gap: var(--spacing-lg);
      }

      .stat-item {
        text-align: center;

        .stat-number {
          font-size: 2rem;
          font-weight: 700;
          color: var(--primary-color);
          margin-bottom: var(--spacing-xs);
        }

        .stat-label {
          font-size: 0.9rem;
          color: var(--text-secondary);
        }
      }
    }
  }
}

// Features Section
.features-section {
  padding: var(--spacing-xl) 0;
  background: var(--bg-primary);

  .section-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--text-primary);
      margin-bottom: var(--spacing-md);
    }

    .section-subtitle {
      font-size: 1.2rem;
      color: var(--text-secondary);
      max-width: 600px;
      margin: 0 auto;
    }
  }

  .features-grid {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: var(--spacing-xl);

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      padding: 0;
    }
  }

  .feature-card {
    background: white;
    padding: var(--spacing-xl);
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-light);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    cursor: pointer;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: var(--primary-gradient);
    }

    &::after {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(245, 112, 112, 0.05), transparent);
      transition: left 0.6s ease;
    }

    &:hover {
      transform: translateY(-10px) scale(1.02);
      box-shadow: var(--shadow-dark);

      &::after {
        left: 100%;
      }

      .feature-arrow {
        transform: translateX(5px);
        opacity: 1;
      }
    }

    &:active {
      transform: translateY(-8px) scale(1.01);
    }

    .feature-icon {
      width: 80px;
      height: 80px;
      margin-bottom: var(--spacing-lg);
      background: var(--primary-gradient);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
      transition: all 0.3s ease;

      &::after {
        content: '';
        position: absolute;
        inset: -2px;
        background: var(--primary-gradient);
        border-radius: 50%;
        z-index: -1;
        filter: blur(10px);
        opacity: 0.3;
        transition: all 0.3s ease;
      }

      &.icon-hover {
        transform: scale(1.1) rotate(5deg);

        &::after {
          opacity: 0.5;
          filter: blur(15px);
        }
      }

      i {
        font-size: 32px;
        color: white;
        transition: all 0.3s ease;
      }
    }

    .feature-title {
      font-size: 1.5rem;
      font-weight: 600;
      color: var(--text-primary);
      margin-bottom: var(--spacing-md);
      transition: color 0.3s ease;
    }

    .feature-description {
      color: var(--text-regular);
      line-height: 1.8;
      margin-bottom: var(--spacing-lg);
      transition: color 0.3s ease;
    }

    .feature-arrow {
      position: absolute;
      bottom: var(--spacing-lg);
      right: var(--spacing-lg);
      color: var(--primary-color);
      font-size: 1.2rem;
      transition: all 0.3s ease;
      opacity: 0.6;

      &.arrow-hover {
        transform: translateX(5px) scale(1.2);
        opacity: 1;
      }
    }
  }
}

// Why Choose Us Section
.why-section {
  padding: var(--spacing-xl) 0;
  background: var(--bg-primary);

  .section-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--text-primary);
      margin-bottom: var(--spacing-md);
    }

    .section-subtitle {
      font-size: 1.2rem;
      color: var(--text-secondary);
      max-width: 600px;
      margin: 0 auto;
    }
  }

  .why-grid {
    max-width: 1000px;
    margin: 0 auto;
    padding: 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: var(--spacing-xl);

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      padding: 0;
    }
  }

  .why-item {
    display: flex;
    align-items: flex-start;
    gap: var(--spacing-lg);
    padding: var(--spacing-xl);
    background: white;
    border-radius: var(--border-radius-large);
    box-shadow: var(--shadow-light);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: var(--primary-gradient);
    }

    &:hover {
      transform: translateY(-5px);
      box-shadow: var(--shadow-base);

      .why-number {
        transform: scale(1.1);
        background: var(--primary-gradient);
        color: white;
      }
    }

    .why-number {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      background: rgba(245, 112, 112, 0.1);
      color: var(--primary-color);
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 1.5rem;
      font-weight: 700;
      transition: all 0.3s ease;
      flex-shrink: 0;
    }

    .why-content {
      flex: 1;

      .why-title {
        font-size: 1.3rem;
        font-weight: 600;
        color: var(--text-primary);
        margin-bottom: var(--spacing-sm);
      }

      .why-description {
        color: var(--text-regular);
        line-height: 1.7;
      }
    }
  }
}

// Technology Stack Section
.tech-section {
  padding: var(--spacing-xl) 0;
  background: var(--bg-secondary);

  .section-header {
    text-align: center;
    margin-bottom: var(--spacing-xl);

    .section-title {
      font-size: 2.5rem;
      font-weight: 700;
      color: var(--text-primary);
      margin-bottom: var(--spacing-md);
    }

    .section-subtitle {
      font-size: 1.2rem;
      color: var(--text-secondary);
    }
  }

  .tech-grid {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0;
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: var(--spacing-lg);

    @media (max-width: 768px) {
      grid-template-columns: repeat(2, 1fr);
      padding: 0;
    }
  }

  .tech-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: var(--spacing-lg);
    background: white;
    border-radius: var(--border-radius-base);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    cursor: pointer;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: linear-gradient(135deg, rgba(245, 112, 112, 0.05) 0%, rgba(255, 142, 142, 0.05) 100%);
      opacity: 0;
      transition: opacity 0.3s ease;
    }

    &:hover,
    &.tech-hover {
      transform: translateY(-8px) scale(1.05);
      box-shadow: var(--shadow-base);

      &::before {
        opacity: 1;
      }

      .tech-icon {
        transform: scale(1.2) rotate(10deg);
        background: var(--primary-gradient);

        i {
          color: white;
        }
      }

      .tech-name {
        color: var(--primary-color);
        font-weight: 600;
      }
    }

    &:active {
      transform: translateY(-6px) scale(1.03);
    }

    .tech-icon {
      width: 50px;
      height: 50px;
      margin-bottom: var(--spacing-sm);
      display: flex;
      align-items: center;
      justify-content: center;
      background: rgba(245, 112, 112, 0.1);
      border-radius: var(--border-radius-base);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      z-index: 1;

      i {
        font-size: 24px;
        color: var(--primary-color);
        transition: all 0.3s ease;
      }
    }

    .tech-name {
      font-size: 0.9rem;
      font-weight: 500;
      color: var(--text-primary);
      text-align: center;
      transition: all 0.3s ease;
      position: relative;
      z-index: 1;
    }

    .tech-tooltip {
      position: absolute;
      bottom: -40px;
      left: 50%;
      transform: translateX(-50%);
      background: var(--text-primary);
      color: white;
      padding: var(--spacing-xs) var(--spacing-sm);
      border-radius: var(--border-radius-small);
      font-size: 12px;
      white-space: nowrap;
      z-index: 10;
      animation: fadeInUp 0.3s ease;

      &::before {
        content: '';
        position: absolute;
        top: -4px;
        left: 50%;
        transform: translateX(-50%);
        width: 0;
        height: 0;
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-bottom: 4px solid var(--text-primary);
      }
    }
  }
}

// Contact Section
.contact-section {
  padding: var(--spacing-xl) 0;
  background: linear-gradient(135deg, var(--primary-color) 0%, #ff8e8e 100%);
  color: white;

  .contact-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0;
  }

  .contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: var(--spacing-xl);
    align-items: center;

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      text-align: center;
    }
  }

  .contact-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    line-height: 1.2;
  }

  .contact-description {
    font-size: 1.2rem;
    line-height: 1.6;
    margin-bottom: var(--spacing-lg);
    opacity: 0.9;
  }

  .contact-features {
    display: flex;
    gap: var(--spacing-lg);
    margin-bottom: var(--spacing-lg);

    @media (max-width: 768px) {
      justify-content: center;
    }

    .contact-feature {
      display: flex;
      align-items: center;
      gap: var(--spacing-xs);
      font-weight: 500;

      i {
        font-size: 1.2rem;
      }
    }
  }

  .contact-actions {
    display: flex;
    gap: var(--spacing-md);
    justify-content: flex-end;

    @media (max-width: 768px) {
      justify-content: center;
    }

    .contact-button {
      padding: var(--spacing-md) var(--spacing-xl);
      border-radius: 50px;
      font-size: 1.1rem;
      font-weight: 600;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

      &:hover {
        transform: translateY(-2px);
      }

      &.secondary {
        background: rgba(255, 255, 255, 0.2);
        border: 2px solid rgba(255, 255, 255, 0.3);
        color: white;

        &:hover {
          background: white;
          color: var(--primary-color);
        }
      }
    }
  }
}

// Footer
.footer {
  background: #1a202c;
  color: white;
  padding: var(--spacing-xl) 0 var(--spacing-lg);

  .footer-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0;
  }

  .footer-content {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: var(--spacing-xl);
    margin-bottom: var(--spacing-xl);

    @media (max-width: 768px) {
      grid-template-columns: 1fr;
      gap: var(--spacing-lg);
    }
  }

  .footer-section {
    .footer-title {
      font-size: 1.5rem;
      font-weight: 700;
      margin-bottom: var(--spacing-md);
      color: white;
    }

    .footer-subtitle {
      font-size: 1.1rem;
      font-weight: 600;
      margin-bottom: var(--spacing-md);
      color: #e2e8f0;
    }

    .footer-description {
      color: #a0aec0;
      line-height: 1.6;
      margin-bottom: var(--spacing-md);
    }

    .footer-links {
      list-style: none;
      padding: 0;
      margin: 0;

      li {
        margin-bottom: var(--spacing-xs);

        a {
          color: #a0aec0;
          text-decoration: none;
          transition: color 0.3s ease;

          &:hover {
            color: white;
          }
        }
      }
    }

    .footer-social {
      .social-link {
        display: inline-flex;
        align-items: center;
        gap: var(--spacing-xs);
        color: #a0aec0;
        text-decoration: none;
        transition: color 0.3s ease;

        &:hover {
          color: var(--primary-color);
        }
      }
    }
  }

  .footer-bottom {
    border-top: 1px solid #2d3748;
    padding-top: var(--spacing-lg);
    text-align: center;

    .footer-copyright {
      color: #a0aec0;
      margin: 0;
    }
  }
}

// Animations
@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-20px);
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

@keyframes pulse-icon {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

@keyframes glow {
  0%, 100% {
    box-shadow: 0 0 5px rgba(245, 112, 112, 0.3);
  }
  50% {
    box-shadow: 0 0 20px rgba(245, 112, 112, 0.6);
  }
}

@keyframes highlight-expand {
  from {
    width: 0;
    opacity: 0;
  }
  to {
    width: 100%;
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-up {
  opacity: 0;
  transform: translateY(30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);

  &.animate {
    opacity: 1;
    transform: translateY(0);
  }
}

.fade-in-down {
  opacity: 0;
  transform: translateY(-30px);
  transition: all 0.8s cubic-bezier(0.4, 0, 0.2, 1);

  &.animate {
    opacity: 1;
    transform: translateY(0);
  }
}

.delay-0 { transition-delay: 0s; }
.delay-1 { transition-delay: 0.2s; }
.delay-2 { transition-delay: 0.4s; }

@media (max-width: 768px) {
  .hero {
    min-height: 80vh;

    .hero-content {
      .main-title {
        font-size: 2.5rem;
      }

      .description {
        font-size: 1.1rem;
      }

      .hero-stats {
        .stat-item .stat-number {
          font-size: 1.5rem;
        }
      }
    }
  }

  .features-section .section-title,
  .tech-section .section-title,
  .why-section .section-title {
    font-size: 2rem;
  }

  .contact-title {
    font-size: 2rem;
  }

  .footer-content {
    grid-template-columns: 1fr;
    text-align: center;
  }
}

// Page transition
.page-transition {
  transition: opacity 0.3s ease;
  opacity: 0.8;
}

// Smooth scrolling
html {
  scroll-behavior: smooth;
}

// Custom scrollbar
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 4px;

  &:hover {
    background: #e55656;
  }
}
</style>
